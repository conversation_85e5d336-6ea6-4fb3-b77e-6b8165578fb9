hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@biomejs/cli-win32-x64@2.2.2':
    '@biomejs/cli-win32-x64': private
  '@clack/core@0.5.0':
    '@clack/core': private
  '@clack/prompts@0.11.0':
    '@clack/prompts': private
  '@esbuild/win32-x64@0.25.9':
    '@esbuild/win32-x64': private
  '@img/sharp-win32-x64@0.34.3':
    '@img/sharp-win32-x64': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.13':
    '@jridgewell/gen-mapping': private
  '@jridgewell/remapping@2.3.5':
    '@jridgewell/remapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.30':
    '@jridgewell/trace-mapping': private
  '@next/env@15.5.2':
    '@next/env': private
  '@next/swc-win32-x64-msvc@15.5.2':
    '@next/swc-win32-x64-msvc': private
  '@radix-ui/primitive@1.1.3':
    '@radix-ui/primitive': private
  '@radix-ui/react-collapsible@1.1.12(@types/react-dom@19.1.9(@types/react@19.1.12))(@types/react@19.1.12)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-collapsible': private
  '@radix-ui/react-collection@1.1.7(@types/react-dom@19.1.9(@types/react@19.1.12))(@types/react@19.1.12)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.2(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.2(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-context': private
  '@radix-ui/react-direction@1.1.1(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.11(@types/react-dom@19.1.9(@types/react@19.1.12))(@types/react@19.1.12)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-id@1.1.1(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-id': private
  '@radix-ui/react-presence@1.1.5(@types/react-dom@19.1.9(@types/react@19.1.12))(@types/react@19.1.12)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.1.3(@types/react-dom@19.1.9(@types/react@19.1.12))(@types/react@19.1.12)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-roving-focus@1.1.11(@types/react-dom@19.1.9(@types/react@19.1.12))(@types/react@19.1.12)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-effect-event@0.0.2(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-use-effect-event': private
  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.1(@types/react@19.1.12)(react@19.1.0)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-visually-hidden@1.2.3(@types/react-dom@19.1.9(@types/react@19.1.12))(@types/react@19.1.12)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-visually-hidden': private
  '@react-email/body@0.1.0(react@19.1.0)':
    '@react-email/body': private
  '@react-email/button@0.2.0(react@19.1.0)':
    '@react-email/button': private
  '@react-email/code-block@0.1.0(react@19.1.0)':
    '@react-email/code-block': private
  '@react-email/code-inline@0.0.5(react@19.1.0)':
    '@react-email/code-inline': private
  '@react-email/column@0.0.13(react@19.1.0)':
    '@react-email/column': private
  '@react-email/container@0.0.15(react@19.1.0)':
    '@react-email/container': private
  '@react-email/font@0.0.9(react@19.1.0)':
    '@react-email/font': private
  '@react-email/head@0.0.12(react@19.1.0)':
    '@react-email/head': private
  '@react-email/heading@0.0.15(react@19.1.0)':
    '@react-email/heading': private
  '@react-email/hr@0.0.11(react@19.1.0)':
    '@react-email/hr': private
  '@react-email/html@0.0.11(react@19.1.0)':
    '@react-email/html': private
  '@react-email/img@0.0.11(react@19.1.0)':
    '@react-email/img': private
  '@react-email/link@0.0.12(react@19.1.0)':
    '@react-email/link': private
  '@react-email/markdown@0.0.15(react@19.1.0)':
    '@react-email/markdown': private
  '@react-email/preview@0.0.13(react@19.1.0)':
    '@react-email/preview': private
  '@react-email/render@1.2.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@react-email/render': private
  '@react-email/row@0.0.12(react@19.1.0)':
    '@react-email/row': private
  '@react-email/section@0.0.16(react@19.1.0)':
    '@react-email/section': private
  '@react-email/tailwind@1.2.2(react@19.1.0)':
    '@react-email/tailwind': private
  '@react-email/text@0.1.5(react@19.1.0)':
    '@react-email/text': private
  '@rollup/rollup-win32-x64-msvc@4.49.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@selderee/plugin-htmlparser2@0.11.0':
    '@selderee/plugin-htmlparser2': private
  '@standard-schema/utils@0.3.0':
    '@standard-schema/utils': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@tailwindcss/node@4.1.12':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.12':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.12':
    '@tailwindcss/oxide': private
  '@trpc/server@11.5.0(typescript@5.9.2)':
    '@trpc/server': private
  '@types/chai@5.2.2':
    '@types/chai': private
  '@types/deep-eql@4.0.2':
    '@types/deep-eql': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/omelette@0.4.5':
    '@types/omelette': private
  '@vitest/expect@3.2.4':
    '@vitest/expect': private
  '@vitest/mocker@3.2.4(vite@7.1.3(@types/node@20.19.11)(jiti@2.5.1)(lightningcss@1.30.1))':
    '@vitest/mocker': private
  '@vitest/pretty-format@3.2.4':
    '@vitest/pretty-format': private
  '@vitest/runner@3.2.4':
    '@vitest/runner': private
  '@vitest/snapshot@3.2.4':
    '@vitest/snapshot': private
  '@vitest/spy@3.2.4':
    '@vitest/spy': private
  '@vitest/utils@3.2.4':
    '@vitest/utils': private
  assertion-error@2.0.1:
    assertion-error: private
  cac@6.7.14:
    cac: private
  caniuse-lite@1.0.30001737:
    caniuse-lite: private
  chai@5.3.3:
    chai: private
  check-error@2.1.1:
    check-error: private
  chownr@3.0.0:
    chownr: private
  citty@0.1.6:
    citty: private
  client-only@0.0.1:
    client-only: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  commander@14.0.0:
    commander: private
  confbox@0.2.2:
    confbox: private
  consola@3.4.2:
    consola: private
  csstype@3.1.3:
    csstype: private
  debug@4.4.1:
    debug: private
  deep-eql@5.0.2:
    deep-eql: private
  deepmerge@4.3.1:
    deepmerge: private
  detect-libc@2.0.4:
    detect-libc: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  domutils@3.2.2:
    domutils: private
  embla-carousel-reactive-utils@8.6.0(embla-carousel@8.6.0):
    embla-carousel-reactive-utils: private
  embla-carousel@8.6.0:
    embla-carousel: private
  enhanced-resolve@5.18.3:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  esbuild@0.25.9:
    esbuild: private
  esm-env@1.2.2:
    esm-env: private
  estree-walker@3.0.3:
    estree-walker: private
  expect-type@1.2.2:
    expect-type: private
  exsolve@1.0.7:
    exsolve: private
  fast-deep-equal@2.0.1:
    fast-deep-equal: private
  fdir@6.5.0(picomatch@4.0.3):
    fdir: private
  framer-motion@12.23.12(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    framer-motion: private
  graceful-fs@4.2.11:
    graceful-fs: private
  html-to-text@9.0.5:
    html-to-text: private
  htmlparser2@8.0.2:
    htmlparser2: private
  is-arrayish@0.3.2:
    is-arrayish: private
  jiti@2.5.1:
    jiti: private
  js-tokens@9.0.1:
    js-tokens: private
  jsonc-parser@3.3.1:
    jsonc-parser: private
  leac@0.6.0:
    leac: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  loupe@3.2.1:
    loupe: private
  magic-string@0.30.18:
    magic-string: private
  marked@7.0.4:
    marked: private
  md-to-react-email@5.0.5(react@19.1.0):
    md-to-react-email: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@3.0.1:
    mkdirp: private
  motion-dom@12.23.12:
    motion-dom: private
  motion-utils@12.23.6:
    motion-utils: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  number-flow@0.5.8:
    number-flow: private
  nypm@0.6.1:
    nypm: private
  parseley@0.12.1:
    parseley: private
  pathe@2.0.3:
    pathe: private
  pathval@2.0.1:
    pathval: private
  peberminta@0.9.0:
    peberminta: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  pkg-types@2.3.0:
    pkg-types: private
  postcss@8.5.6:
    postcss: private
  prettier@3.6.2:
    prettier: private
  prismjs@1.30.0:
    prismjs: private
  react-promise-suspense@0.3.4:
    react-promise-suspense: private
  rollup@4.49.0:
    rollup: private
  scheduler@0.26.0:
    scheduler: private
  selderee@0.11.0:
    selderee: private
  semver@7.7.2:
    semver: private
  sharp@0.34.3:
    sharp: private
  siginfo@2.0.0:
    siginfo: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  sisteransi@1.0.5:
    sisteransi: private
  source-map-js@1.2.1:
    source-map-js: private
  stackback@0.0.2:
    stackback: private
  std-env@3.9.0:
    std-env: private
  strip-literal@3.0.0:
    strip-literal: private
  styled-jsx@5.1.6(react@19.1.0):
    styled-jsx: private
  tapable@2.2.3:
    tapable: private
  tar@7.4.3:
    tar: private
  tinybench@2.9.0:
    tinybench: private
  tinyexec@1.0.1:
    tinyexec: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tinypool@1.1.1:
    tinypool: private
  tinyrainbow@2.0.0:
    tinyrainbow: private
  tinyspy@4.0.3:
    tinyspy: private
  trpc-cli@0.10.2(typescript@5.9.2):
    trpc-cli: private
  tslib@2.8.1:
    tslib: private
  undici-types@6.21.0:
    undici-types: private
  vite-node@3.2.4(@types/node@20.19.11)(jiti@2.5.1)(lightningcss@1.30.1):
    vite-node: private
  vite@7.1.3(@types/node@20.19.11)(jiti@2.5.1)(lightningcss@1.30.1):
    vite: private
  vitest@3.2.4(@types/node@20.19.11)(jiti@2.5.1)(lightningcss@1.30.1):
    vitest: private
  why-is-node-running@2.3.0:
    why-is-node-running: private
  yallist@5.0.0:
    yallist: private
  zod-to-json-schema@3.24.6(zod@3.25.76):
    zod-to-json-schema: private
  zod@4.1.5:
    zod: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.15.0
pendingBuilds: []
prunedAt: Fri, 29 Aug 2025 13:22:20 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@biomejs/cli-darwin-arm64@2.2.0'
  - '@biomejs/cli-darwin-arm64@2.2.2'
  - '@biomejs/cli-darwin-x64@2.2.0'
  - '@biomejs/cli-darwin-x64@2.2.2'
  - '@biomejs/cli-linux-arm64-musl@2.2.0'
  - '@biomejs/cli-linux-arm64-musl@2.2.2'
  - '@biomejs/cli-linux-arm64@2.2.0'
  - '@biomejs/cli-linux-arm64@2.2.2'
  - '@biomejs/cli-linux-x64-musl@2.2.0'
  - '@biomejs/cli-linux-x64-musl@2.2.2'
  - '@biomejs/cli-linux-x64@2.2.0'
  - '@biomejs/cli-linux-x64@2.2.2'
  - '@biomejs/cli-win32-arm64@2.2.0'
  - '@biomejs/cli-win32-arm64@2.2.2'
  - '@emnapi/runtime@1.5.0'
  - '@esbuild/aix-ppc64@0.25.9'
  - '@esbuild/android-arm64@0.25.9'
  - '@esbuild/android-arm@0.25.9'
  - '@esbuild/android-x64@0.25.9'
  - '@esbuild/darwin-arm64@0.25.9'
  - '@esbuild/darwin-x64@0.25.9'
  - '@esbuild/freebsd-arm64@0.25.9'
  - '@esbuild/freebsd-x64@0.25.9'
  - '@esbuild/linux-arm64@0.25.9'
  - '@esbuild/linux-arm@0.25.9'
  - '@esbuild/linux-ia32@0.25.9'
  - '@esbuild/linux-loong64@0.25.9'
  - '@esbuild/linux-mips64el@0.25.9'
  - '@esbuild/linux-ppc64@0.25.9'
  - '@esbuild/linux-riscv64@0.25.9'
  - '@esbuild/linux-s390x@0.25.9'
  - '@esbuild/linux-x64@0.25.9'
  - '@esbuild/netbsd-arm64@0.25.9'
  - '@esbuild/netbsd-x64@0.25.9'
  - '@esbuild/openbsd-arm64@0.25.9'
  - '@esbuild/openbsd-x64@0.25.9'
  - '@esbuild/openharmony-arm64@0.25.9'
  - '@esbuild/sunos-x64@0.25.9'
  - '@esbuild/win32-arm64@0.25.9'
  - '@esbuild/win32-ia32@0.25.9'
  - '@img/sharp-darwin-arm64@0.34.3'
  - '@img/sharp-darwin-x64@0.34.3'
  - '@img/sharp-libvips-darwin-arm64@1.2.0'
  - '@img/sharp-libvips-darwin-x64@1.2.0'
  - '@img/sharp-libvips-linux-arm64@1.2.0'
  - '@img/sharp-libvips-linux-arm@1.2.0'
  - '@img/sharp-libvips-linux-ppc64@1.2.0'
  - '@img/sharp-libvips-linux-s390x@1.2.0'
  - '@img/sharp-libvips-linux-x64@1.2.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.2.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.2.0'
  - '@img/sharp-linux-arm64@0.34.3'
  - '@img/sharp-linux-arm@0.34.3'
  - '@img/sharp-linux-ppc64@0.34.3'
  - '@img/sharp-linux-s390x@0.34.3'
  - '@img/sharp-linux-x64@0.34.3'
  - '@img/sharp-linuxmusl-arm64@0.34.3'
  - '@img/sharp-linuxmusl-x64@0.34.3'
  - '@img/sharp-wasm32@0.34.3'
  - '@img/sharp-win32-arm64@0.34.3'
  - '@img/sharp-win32-ia32@0.34.3'
  - '@next/swc-darwin-arm64@15.5.2'
  - '@next/swc-darwin-x64@15.5.2'
  - '@next/swc-linux-arm64-gnu@15.5.2'
  - '@next/swc-linux-arm64-musl@15.5.2'
  - '@next/swc-linux-x64-gnu@15.5.2'
  - '@next/swc-linux-x64-musl@15.5.2'
  - '@next/swc-win32-arm64-msvc@15.5.2'
  - '@rollup/rollup-android-arm-eabi@4.49.0'
  - '@rollup/rollup-android-arm64@4.49.0'
  - '@rollup/rollup-darwin-arm64@4.49.0'
  - '@rollup/rollup-darwin-x64@4.49.0'
  - '@rollup/rollup-freebsd-arm64@4.49.0'
  - '@rollup/rollup-freebsd-x64@4.49.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.49.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.49.0'
  - '@rollup/rollup-linux-arm64-gnu@4.49.0'
  - '@rollup/rollup-linux-arm64-musl@4.49.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.49.0'
  - '@rollup/rollup-linux-ppc64-gnu@4.49.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.49.0'
  - '@rollup/rollup-linux-riscv64-musl@4.49.0'
  - '@rollup/rollup-linux-s390x-gnu@4.49.0'
  - '@rollup/rollup-linux-x64-gnu@4.49.0'
  - '@rollup/rollup-linux-x64-musl@4.49.0'
  - '@rollup/rollup-win32-arm64-msvc@4.49.0'
  - '@rollup/rollup-win32-ia32-msvc@4.49.0'
  - '@tailwindcss/oxide-android-arm64@4.1.12'
  - '@tailwindcss/oxide-darwin-arm64@4.1.12'
  - '@tailwindcss/oxide-darwin-x64@4.1.12'
  - '@tailwindcss/oxide-freebsd-x64@4.1.12'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.12'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.12'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.12'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.12'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.12'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.12'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.12'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Documents\nProjects\better\node_modules\.pnpm
virtualStoreDirMaxLength: 60
