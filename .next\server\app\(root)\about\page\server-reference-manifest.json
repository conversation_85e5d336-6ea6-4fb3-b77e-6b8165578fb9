{"node": {"401553368a882499aede4e05ebfdb3ca7882231805": {"workers": {"app/(root)/about/page": {"moduleId": "[project]/.next-internal/server/app/(root)/about/page/actions.js { ACTIONS_MODULE0 => \"[project]/server/subscribe.action.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false, "exportedName": "subscribe", "filename": "server/subscribe.action.ts"}}, "layer": {"app/(root)/about/page": "action-browser"}, "exportedName": "subscribe", "filename": "server/subscribe.action.ts"}}, "edge": {}}