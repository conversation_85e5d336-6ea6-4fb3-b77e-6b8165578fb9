self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"401553368a882499aede4e05ebfdb3ca7882231805\": {\n      \"workers\": {\n        \"app/(root)/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(root)/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/server/subscribe.action.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"subscribe\",\n          \"filename\": \"server/subscribe.action.ts\"\n        }\n      },\n      \"layer\": {\n        \"app/(root)/page\": \"action-browser\"\n      },\n      \"filename\": \"server/subscribe.action.ts\",\n      \"exportedName\": \"subscribe\"\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"t/Y1PTgqZUp7qBhsAcg0nxwiG/yBPR5XdglAzhx+rL8=\"\n}"