import{validateFieldsNatively as r,toNestErrors as e}from"@hookform/resolvers";function t(r,e,o){void 0===o&&(o=[]);var n=function(){var n=[].concat(o,[a]),i=r[a];Array.isArray(i)?i.forEach(function(r,o){t(r,e,[].concat(n,[o]))}):"object"==typeof i&&null!==i?t(i,e,n):"string"==typeof i&&(e[n.join(".")]={type:"validation",message:i})};for(var a in r)n()}function o(r,e){var o={};return t(r,o),o}function n(t){return function(n,a,i){try{var s=t.validate(n),c=0===Object.keys(s).length;return i.shouldUseNativeValidation&&r({},i),Promise.resolve(c?{values:n,errors:{}}:{values:{},errors:e(o(s),i)})}catch(r){return Promise.reject(r)}}}function a(t){return function(n,a,i){try{return Promise.resolve(t.validateAsync(n)).then(function(t){var a=0===Object.keys(t).length;return i.shouldUseNativeValidation&&r({},i),a?{values:n,errors:{}}:{values:{},errors:e(o(t),i)}})}catch(r){return Promise.reject(r)}}}export{a as fluentAsyncValidationResolver,n as fluentValidationResolver};
//# sourceMappingURL=fluentvalidation-ts.module.js.map
