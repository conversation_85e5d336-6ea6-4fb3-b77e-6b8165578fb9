{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/aspect-ratio.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as AspectRatioPrimitive from \"@radix-ui/react-aspect-ratio\"\n\nfunction AspectRatio({\n  ...props\n}: React.ComponentProps<typeof AspectRatioPrimitive.Root>) {\n  return <AspectRatioPrimitive.Root data-slot=\"aspect-ratio\" {...props} />\n}\n\nexport { AspectRatio }\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAIA,SAAS,YAAY,KAEoC;QAFpC,EACnB,GAAG,OACoD,GAFpC;IAGnB,qBAAO,4TAAC,6RAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;KAJS", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/%40radix-ui%2Breact-aspect-rati_f674f65a24f4a42a9a85520fdf6e953c/node_modules/%40radix-ui/react-aspect-ratio/src/aspect-ratio.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * AspectRatio\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'AspectRatio';\n\ntype AspectRatioElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface AspectRatioProps extends PrimitiveDivProps {\n  ratio?: number;\n}\n\nconst AspectRatio = React.forwardRef<AspectRatioElement, AspectRatioProps>(\n  (props, forwardedRef) => {\n    const { ratio = 1 / 1, style, ...aspectRatioProps } = props;\n    return (\n      <div\n        style={{\n          // ensures inner element is contained\n          position: 'relative',\n          // ensures padding bottom trick maths works\n          width: '100%',\n          paddingBottom: `${100 / ratio}%`,\n        }}\n        data-radix-aspect-ratio-wrapper=\"\"\n      >\n        <Primitive.div\n          {...aspectRatioProps}\n          ref={forwardedRef}\n          style={{\n            ...style,\n            // ensures children expand in ratio\n            position: 'absolute',\n            top: 0,\n            right: 0,\n            bottom: 0,\n            left: 0,\n          }}\n        />\n      </div>\n    );\n  }\n);\n\nAspectRatio.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = AspectRatio;\n\nexport {\n  AspectRatio,\n  //\n  Root,\n};\nexport type { AspectRatioProps };\n"], "names": [], "mappings": ";;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AA4BlB;;;;AAtBR,IAAM,OAAO;AAQb,IAAM,cAAoB,0SAAA,CACxB,CAAC,OAAO,iBAAiB;IACvB,MAAM,EAAE,QAAQ,IAAI,CAAA,EAAG,KAAA,EAAO,GAAG,iBAAiB,CAAA,GAAI;IACtD,OACE,aAAA,GAAA,IAAA,4SAAA,EAAC,OAAA;QACC,OAAO;YAAA,qCAAA;YAEL,UAAU;YAAA,2CAAA;YAEV,OAAO;YACP,eAAe,GAAc,OAAX,MAAM,KAAK,EAAA;QAC/B;QACA,mCAAgC;QAEhC,UAAA,aAAA,GAAA,IAAA,4SAAA,EAAC,4RAAA,CAAU,GAAA,EAAV;YACE,GAAG,gBAAA;YACJ,KAAK;YACL,OAAO;gBACL,GAAG,KAAA;gBAAA,mCAAA;gBAEH,UAAU;gBACV,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,MAAM;YACR;QAAA;IACF;AAGN;AAGF,YAAY,WAAA,GAAc;AAI1B,IAAM,OAAO", "debugId": null}}]}