{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/emails/subscribe-template.tsx"], "sourcesContent": ["import {\n  <PERSON>,\n  <PERSON><PERSON>,\n  <PERSON>umn,\n  Con<PERSON>er,\n  <PERSON><PERSON>,\n  <PERSON>,\n  Hr,\n  Html,\n  Link,\n  Preview,\n  Row,\n  Section,\n  Tailwind,\n  Text,\n} from '@react-email/components';\n\ntype EmailTemplateProps = {\n  email: string;\n};\n\nexport const SubscribedTemplate = ({ email }: EmailTemplateProps) => {\n  const currentDate = new Date().toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n\n  // Ensure the email is displayed safely\n  const safeEmail = email || '[No email provided]';\n\n  return (\n    <Html>\n      <Head>\n        <Font\n          fallbackFontFamily=\"Arial\"\n          fontFamily=\"Roboto\"\n          fontStyle=\"normal\"\n          fontWeight={400}\n          webFont={{\n            url: 'https://fonts.gstatic.com/s/roboto/v27/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2',\n            format: 'woff2',\n          }}\n        />\n      </Head>\n      <Preview>\n        New subscriber: {safeEmail} has joined your mailing list!\n      </Preview>\n      <Tailwind>\n        <Body className=\"mx-auto my-auto bg-gray-50 font-sans\">\n          <Container className=\"mx-auto my-8 max-w-[600px]\">\n            {/* Header */}\n            <Section className=\"rounded-t-lg bg-blue-600 px-8 py-6\">\n              <Row>\n                <Column>\n                  <Text className=\"text-center font-bold text-3xl text-white\">\n                    Rathon\n                  </Text>\n                </Column>\n              </Row>\n            </Section>\n\n            {/* Main Content */}\n            <Section className=\"rounded-b-lg bg-white px-8 py-10 shadow-sm\">\n              <Row>\n                <Column>\n                  <Text className=\"mb-5 font-bold text-2xl text-gray-800\">\n                    New Subscriber Alert\n                  </Text>\n\n                  <Text className=\"mb-5 text-gray-700\">\n                    You have a new subscriber to your mailing list! Someone is\n                    interested in hearing more about Rathon&apos;s services and\n                    updates.\n                  </Text>\n\n                  {/* Subscriber Info */}\n                  <Container className=\"mb-6 rounded-md border-blue-500 border-l-4 bg-blue-50 p-5\">\n                    <Text className=\"mb-1 font-medium text-base text-blue-800\">\n                      Subscriber Details:\n                    </Text>\n                    <Text className=\"mb-0 text-blue-800\">\n                      <strong>Email:</strong> {safeEmail}\n                    </Text>\n                    <Text className=\"mb-0 text-blue-800\">\n                      <strong>Subscribed on:</strong> {currentDate}\n                    </Text>\n                  </Container>\n\n                  {/* Action Button */}\n                  <Section className=\"mb-8 text-center\">\n                    <Button\n                      className=\"rounded-md bg-blue-600 px-6 py-3 font-medium text-white hover:bg-blue-700\"\n                      href={`mailto:${safeEmail}`}\n                    >\n                      Contact Subscriber\n                    </Button>\n                  </Section>\n\n                  <Hr className=\"my-6 border-gray-200\" />\n\n                  <Text className=\"mb-4 text-gray-700\">\n                    Remember to maintain GDPR compliance when reaching out to\n                    new subscribers.\n                  </Text>\n                </Column>\n              </Row>\n            </Section>\n\n            {/* Footer */}\n            <Section className=\"px-8 py-6\">\n              <Text className=\"text-center text-gray-500 text-xs\">\n                © {new Date().getFullYear()} Rathon. All rights reserved.\n              </Text>\n              <Text className=\"text-center text-gray-500 text-xs\">\n                This is an automated notification from your website&apos;s\n                subscription system.\n              </Text>\n              <Text className=\"text-center text-gray-500 text-xs\">\n                <Link className=\"text-blue-500 underline\" href=\"#\">\n                  Unsubscribe\n                </Link>{' '}\n                from these notifications\n              </Text>\n            </Section>\n          </Container>\n        </Body>\n      </Tailwind>\n    </Html>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAqBO,MAAM,qBAAqB,CAAC,EAAE,KAAK,EAAsB;IAC9D,MAAM,cAAc,IAAI,OAAO,kBAAkB,CAAC,SAAS;QACzD,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;IAEA,uCAAuC;IACvC,MAAM,YAAY,SAAS;IAE3B,qBACE,6WAAC,8PAAI;;0BACH,6WAAC,8PAAI;0BACH,cAAA,6WAAC,6PAAI;oBACH,oBAAmB;oBACnB,YAAW;oBACX,WAAU;oBACV,YAAY;oBACZ,SAAS;wBACP,KAAK;wBACL,QAAQ;oBACV;;;;;;;;;;;0BAG<PERSON>,6WAAC,uQAAO;;oBAAC;oBACU;oBAAU;;;;;;;0BAE7B,6WAAC,yQAAQ;0BACP,cAAA,6WAAC,6PAAI;oBAAC,WAAU;8BACd,cAAA,6WAAC,6QAAS;wBAAC,WAAU;;0CAEnB,6WAAC,uQAAO;gCAAC,WAAU;0CACjB,cAAA,6WAAC,2PAAG;8CACF,cAAA,6WAAC,oQAAM;kDACL,cAAA,6WAAC,6PAAI;4CAAC,WAAU;sDAA4C;;;;;;;;;;;;;;;;;;;;;0CAQlE,6WAAC,uQAAO;gCAAC,WAAU;0CACjB,cAAA,6WAAC,2PAAG;8CACF,cAAA,6WAAC,oQAAM;;0DACL,6WAAC,6PAAI;gDAAC,WAAU;0DAAwC;;;;;;0DAIxD,6WAAC,6PAAI;gDAAC,WAAU;0DAAqB;;;;;;0DAOrC,6WAAC,6QAAS;gDAAC,WAAU;;kEACnB,6WAAC,6PAAI;wDAAC,WAAU;kEAA2C;;;;;;kEAG3D,6WAAC,6PAAI;wDAAC,WAAU;;0EACd,6WAAC;0EAAO;;;;;;4DAAe;4DAAE;;;;;;;kEAE3B,6WAAC,6PAAI;wDAAC,WAAU;;0EACd,6WAAC;0EAAO;;;;;;4DAAuB;4DAAE;;;;;;;;;;;;;0DAKrC,6WAAC,uQAAO;gDAAC,WAAU;0DACjB,cAAA,6WAAC,mQAAM;oDACL,WAAU;oDACV,MAAM,CAAC,OAAO,EAAE,WAAW;8DAC5B;;;;;;;;;;;0DAKH,6WAAC,wPAAE;gDAAC,WAAU;;;;;;0DAEd,6WAAC,6PAAI;gDAAC,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;;;;;0CAS3C,6WAAC,uQAAO;gCAAC,WAAU;;kDACjB,6WAAC,6PAAI;wCAAC,WAAU;;4CAAoC;4CAC/C,IAAI,OAAO,WAAW;4CAAG;;;;;;;kDAE9B,6WAAC,6PAAI;wCAAC,WAAU;kDAAoC;;;;;;kDAIpD,6WAAC,6PAAI;wCAAC,WAAU;;0DACd,6WAAC,8PAAI;gDAAC,WAAU;gDAA0B,MAAK;0DAAI;;;;;;4CAE3C;4CAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5B", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/server/subscribe.action.ts"], "sourcesContent": ["'use server';\n\nimport { Resend } from 'resend';\nimport { SubscribedTemplate } from '@/components/emails/subscribe-template';\nimport type { TSubFormSchema } from './schema';\n\n// Initialize Resend client with API key from environment variables\nconst resendClient = new Resend(process.env.RESEND_API_KEY);\n\nexport async function subscribe(formData: TSubFormSchema) {\n  try {\n    const { email } = formData;\n\n    const response = await resendClient.emails.send({\n      from: 'Rathon <<EMAIL>>',\n      to: ['<EMAIL>'],\n      subject: 'New Subscriber from Rathon Website',\n      react: SubscribedTemplate({ email }) as React.ReactElement,\n      replyTo: email,\n      tags: [{ name: 'source', value: 'website_subscribe' }],\n    });\n\n    if (response.error) {\n      return { success: false, error: response.error };\n    }\n\n    return { success: true };\n  } catch (error) {\n    return { success: false, error };\n  }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;;;AAGA,mEAAmE;AACnE,MAAM,eAAe,IAAI,8PAAM,CAAC,QAAQ,GAAG,CAAC,cAAc;AAEnD,eAAe,UAAU,QAAwB;IACtD,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG;QAElB,MAAM,WAAW,MAAM,aAAa,MAAM,CAAC,IAAI,CAAC;YAC9C,MAAM;YACN,IAAI;gBAAC;aAAqB;YAC1B,SAAS;YACT,OAAO,IAAA,oKAAkB,EAAC;gBAAE;YAAM;YAClC,SAAS;YACT,MAAM;gBAAC;oBAAE,MAAM;oBAAU,OAAO;gBAAoB;aAAE;QACxD;QAEA,IAAI,SAAS,KAAK,EAAE;YAClB,OAAO;gBAAE,SAAS;gBAAO,OAAO,SAAS,KAAK;YAAC;QACjD;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;;;IArBsB;;AAAA,8WAAA", "debugId": null}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/.next-internal/server/app/%28root%29/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {subscribe as '401553368a882499aede4e05ebfdb3ca7882231805'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/features/home/<USER>'s on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/features/home/<USER>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,uYAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/features/home/<USER>'s on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/features/home/<USER>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,uYAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/features/home/<USER>'s on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/features/home/<USER>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,uYAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/features/home/<USER>'s on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/features/home/<USER>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,uYAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["import FeatureTableLg from './compare-table-lg';\nimport FeatureTableSm from './compare-table-sm';\n\nexport default function Comparison() {\n  return (\n    <section className=\"mb-40 w-full\">\n      <div className=\"mx-auto max-w-6xl px-0 md:px-10 2xl:max-w-7xl\">\n        <h2 className=\"mx-auto mb-14 max-w-[320px] whitespace-pre-wrap text-center font-aeonik font-medium text-3xl leading-tight sm:max-w-2xl sm:text-4xl md:text-4xl lg:text-5xl\">\n          How does Highlight AI compare against other tools?\n        </h2>\n        <div className=\"flex flex-col items-center justify-center gap-8\">\n          <div className=\"flex w-full items-center justify-center align-center font-aeonik\">\n            <FeatureTableLg />\n            <FeatureTableSm />\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,6WAAC;QAAQ,WAAU;kBACjB,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAG,WAAU;8BAA8J;;;;;;8BAG5K,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,sJAAc;;;;;0CACf,6WAAC,sJAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/kibo-ui/image-zoom/index.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ImageZoom = registerClientReference(\n    function() { throw new Error(\"Attempted to call ImageZoom() from the server but ImageZoom is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/kibo-ui/image-zoom/index.tsx <module evaluation>\",\n    \"ImageZoom\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,uYAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,4EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 537, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/kibo-ui/image-zoom/index.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ImageZoom = registerClientReference(\n    function() { throw new Error(\"Attempted to call ImageZoom() from the server but ImageZoom is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/kibo-ui/image-zoom/index.tsx\",\n    \"ImageZoom\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,uYAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,wDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 551, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["import Image from 'next/image';\nimport { ImageZoom } from '@/components/ui/kibo-ui/image-zoom';\nimport { cn } from '@/lib/utils';\n\n/** biome-ignore-all lint/performance/noImgElement: <explanation> */\nexport default function EditorFeatureHome() {\n  return (\n    <section className=\"mb-40\">\n      <div className=\"mx-auto max-w-6xl px-0 md:px-10 2xl:max-w-7xl\">\n        <div className=\"flex flex-col items-center gap-[80px] font-aeonik\">\n          {/* Desktop */}\n          <div className=\"flex w-full flex-col items-start gap-[60px]\">\n            <div className=\"flex w-full flex-col items-start gap-5\">\n              <div className=\"font-medium text-brand-500 text-xl\">\n                Desktop Intelligence\n              </div>\n              <h2 className=\"max-w-[320px] font-aeonik font-medium text-3xl text-white/80 leading-none tracking-[-1px] sm:max-w-2xl md:max-w-none md:text-[42px]\">\n                <PERSON><PERSON> understands what you see and hear.\n              </h2>\n            </div>\n            <a\n              className=\"group relative flex aspect-square w-full cursor-pointer items-center justify-center overflow-hidden rounded-[20px] bg-[#303030] md:aspect-auto\"\n              href=\"/chat\"\n            >\n              <img\n                alt=\"Intelligence Hero\"\n                className=\"h-full w-full rounded-[20px] object-cover transition-transform duration-300 group-hover:scale-105 md:h-auto md:object-contain\"\n                height=\"622\"\n                src=\"/desktop.webp\"\n                width=\"1200\"\n              />\n            </a>\n          </div>\n          {/* grids for more */}\n          <div className=\"grid w-full grid-cols-1 items-start gap-16 lg:grid-cols-2\">\n            <div className=\"flex flex-1 flex-col gap-[50px]\">\n              <div className=\"flex flex-col gap-3\">\n                <h3 className=\"font-aeonik font-medium text-2xl text-white leading-none tracking-[-1.2px] md:text-[30px]\">\n                  Record Meetings\n                </h3>\n                <p className=\"mx-auto mb-2 max-w-3xl font-aeonik text-lg text-muted-foreground sm:text-xl\">\n                  <span>\n                    Record meetings, lessons, interviews, and more. Instant\n                    transcripts &amp; summaries.\n                  </span>\n                </p>\n                <a\n                  className=\"flex items-center gap-2 font-medium text-[16px] text-brand-500 transition-colors hover:text-brand-500/80\"\n                  href=\"/meetings\"\n                >\n                  Learn More\n                  <svg\n                    className=\"lucide lucide-chevron-right h-4 w-4\"\n                    fill=\"none\"\n                    height=\"24\"\n                    stroke=\"currentColor\"\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth=\"2\"\n                    viewBox=\"0 0 24 24\"\n                    width=\"24\"\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                  >\n                    <title>Right Arrow</title>\n                    <path d=\"m9 18 6-6-6-6\" />\n                  </svg>\n                </a>\n              </div>\n              <div className=\"flex flex-col gap-2 md:gap-[25px]\">\n                <div className=\"group relative flex h-[450px] w-full cursor-pointer items-center justify-center overflow-hidden rounded-[20px] bg-[#303030]\">\n                  <ImageZoom\n                    backdropClassName={cn(\n                      '[&_[data-rmiz-modal-overlay=\"visible\"]]:bg-brand-100/80'\n                    )}\n                    className=\"relative aspect-square h-full w-full overflow-hidden rounded-lg\"\n                    zoomMargin={100}\n                  >\n                    <Image\n                      alt=\"Recording interface\"\n                      className=\"h-full w-full object-cover transition-transform duration-300 group-hover:scale-105\"\n                      height=\"450\"\n                      loading=\"lazy\"\n                      sizes=\"100vw\"\n                      src=\"/desktop.webp\"\n                      style={{\n                        position: 'absolute',\n                        height: '100%',\n                        width: '100%',\n                        inset: '0px',\n                        color: 'transparent',\n                      }}\n                      width=\"450\"\n                    />\n                  </ImageZoom>\n                </div>\n                <div className=\"flex flex-col gap-3 px-3 py-2 md:flex-row md:items-center md:justify-between md:gap-0\">\n                  <span className=\"font-aeonik font-medium text-base text-white/50 tracking-[-0.4px] md:text-[20px]\">\n                    Works with all your meetings platforms.\n                  </span>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex flex-1 flex-col gap-[50px]\">\n              <div className=\"flex flex-col gap-3\">\n                <h3 className=\"font-aeonik font-medium text-2xl text-white leading-none tracking-[-1.2px] md:text-[30px]\">\n                  Record Meetings\n                </h3>\n                <p className=\"mx-auto mb-2 max-w-3xl font-aeonik text-lg text-muted-foreground sm:text-xl\">\n                  <span>\n                    Record meetings, lessons, interviews, and more. Instant\n                    transcripts &amp; summaries.\n                  </span>\n                </p>\n                <a\n                  className=\"flex items-center gap-2 font-medium text-[16px] text-brand-500 transition-colors hover:text-brand-500/80\"\n                  href=\"/meetings\"\n                >\n                  Learn More\n                  <svg\n                    className=\"lucide lucide-chevron-right h-4 w-4\"\n                    fill=\"none\"\n                    height=\"24\"\n                    stroke=\"currentColor\"\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth=\"2\"\n                    viewBox=\"0 0 24 24\"\n                    width=\"24\"\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                  >\n                    <title>Right Arrow</title>\n                    <path d=\"m9 18 6-6-6-6\" />\n                  </svg>\n                </a>\n              </div>\n              <div className=\"flex flex-col gap-2 md:gap-[25px]\">\n                <div className=\"group relative flex h-[450px] w-full cursor-pointer items-center justify-center overflow-hidden rounded-[20px] bg-[#303030]\">\n                  <ImageZoom\n                    backdropClassName={cn(\n                      '[&_[data-rmiz-modal-overlay=\"visible\"]]:bg-brand-100/80'\n                    )}\n                    className=\"relative aspect-square h-full w-full overflow-hidden rounded-lg\"\n                    zoomMargin={100}\n                  >\n                    <Image\n                      alt=\"Recording interface\"\n                      className=\"h-full w-full object-cover transition-transform duration-300 group-hover:scale-105\"\n                      height=\"450\"\n                      loading=\"lazy\"\n                      sizes=\"100vw\"\n                      src=\"/desktop.webp\"\n                      style={{\n                        position: 'absolute',\n                        height: '100%',\n                        width: '100%',\n                        inset: '0px',\n                        color: 'transparent',\n                      }}\n                      width=\"450\"\n                    />\n                  </ImageZoom>\n                </div>\n                <div className=\"flex flex-col gap-3 px-3 py-2 md:flex-row md:items-center md:justify-between md:gap-0\">\n                  <span className=\"font-aeonik font-medium text-base text-white/50 tracking-[-0.4px] md:text-[20px]\">\n                    Works with all your meetings platforms.\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAGe,SAAS;IACtB,qBACE,6WAAC;QAAQ,WAAU;kBACjB,cAAA,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDAAqC;;;;;;kDAGpD,6WAAC;wCAAG,WAAU;kDAAsI;;;;;;;;;;;;0CAItJ,6WAAC;gCACC,WAAU;gCACV,MAAK;0CAEL,cAAA,6WAAC;oCACC,KAAI;oCACJ,WAAU;oCACV,QAAO;oCACP,KAAI;oCACJ,OAAM;;;;;;;;;;;;;;;;;kCAKZ,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAG,WAAU;0DAA4F;;;;;;0DAG1G,6WAAC;gDAAE,WAAU;0DACX,cAAA,6WAAC;8DAAK;;;;;;;;;;;0DAKR,6WAAC;gDACC,WAAU;gDACV,MAAK;;oDACN;kEAEC,6WAAC;wDACC,WAAU;wDACV,MAAK;wDACL,QAAO;wDACP,QAAO;wDACP,eAAc;wDACd,gBAAe;wDACf,aAAY;wDACZ,SAAQ;wDACR,OAAM;wDACN,OAAM;;0EAEN,6WAAC;0EAAM;;;;;;0EACP,6WAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;kDAId,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC,sKAAS;oDACR,mBAAmB,IAAA,kHAAE,EACnB;oDAEF,WAAU;oDACV,YAAY;8DAEZ,cAAA,6WAAC,uQAAK;wDACJ,KAAI;wDACJ,WAAU;wDACV,QAAO;wDACP,SAAQ;wDACR,OAAM;wDACN,KAAI;wDACJ,OAAO;4DACL,UAAU;4DACV,QAAQ;4DACR,OAAO;4DACP,OAAO;4DACP,OAAO;wDACT;wDACA,OAAM;;;;;;;;;;;;;;;;0DAIZ,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAK,WAAU;8DAAmF;;;;;;;;;;;;;;;;;;;;;;;0CAMzG,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAG,WAAU;0DAA4F;;;;;;0DAG1G,6WAAC;gDAAE,WAAU;0DACX,cAAA,6WAAC;8DAAK;;;;;;;;;;;0DAKR,6WAAC;gDACC,WAAU;gDACV,MAAK;;oDACN;kEAEC,6WAAC;wDACC,WAAU;wDACV,MAAK;wDACL,QAAO;wDACP,QAAO;wDACP,eAAc;wDACd,gBAAe;wDACf,aAAY;wDACZ,SAAQ;wDACR,OAAM;wDACN,OAAM;;0EAEN,6WAAC;0EAAM;;;;;;0EACP,6WAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;kDAId,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC,sKAAS;oDACR,mBAAmB,IAAA,kHAAE,EACnB;oDAEF,WAAU;oDACV,YAAY;8DAEZ,cAAA,6WAAC,uQAAK;wDACJ,KAAI;wDACJ,WAAU;wDACV,QAAO;wDACP,SAAQ;wDACR,OAAM;wDACN,KAAI;wDACJ,OAAO;4DACL,UAAU;4DACV,QAAQ;4DACR,OAAO;4DACP,OAAO;4DACP,OAAO;wDACT;wDACA,OAAM;;;;;;;;;;;;;;;;0DAIZ,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAK,WAAU;8DAAmF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrH", "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/accordion.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Accordion = registerClientReference(\n    function() { throw new Error(\"Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx <module evaluation>\",\n    \"Accordion\",\n);\nexport const AccordionContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx <module evaluation>\",\n    \"AccordionContent\",\n);\nexport const AccordionItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx <module evaluation>\",\n    \"AccordionItem\",\n);\nexport const AccordionTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx <module evaluation>\",\n    \"AccordionTrigger\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,uYAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA;AAEG,MAAM,mBAAmB,IAAA,uYAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6DACA;AAEG,MAAM,gBAAgB,IAAA,uYAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,6DACA;AAEG,MAAM,mBAAmB,IAAA,uYAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/accordion.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Accordion = registerClientReference(\n    function() { throw new Error(\"Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx\",\n    \"Accordion\",\n);\nexport const AccordionContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx\",\n    \"AccordionContent\",\n);\nexport const AccordionItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx\",\n    \"AccordionItem\",\n);\nexport const AccordionTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx\",\n    \"AccordionTrigger\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,uYAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA;AAEG,MAAM,mBAAmB,IAAA,uYAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,yCACA;AAEG,MAAM,gBAAgB,IAAA,uYAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yCACA;AAEG,MAAM,mBAAmB,IAAA,uYAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,yCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1006, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1014, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["import {\n  Accordion,\n  AccordionContent,\n  AccordionItem,\n  AccordionTrigger,\n} from '@/components/ui/accordion';\n\nconst faqs = [\n  {\n    question: 'Does Highlight work on my device?',\n    answer:\n      \"We are the only AI assistant that works across both Windows and Mac. Our team is fully committed to developing a product that meets users wherever they're at.\",\n  },\n  {\n    question: 'How is my data handled?',\n    answer:\n      \"We don't see your data. Period. The only time your data leaves your computer is when you attach it to a cloud LLM query or if you had enabled task detection, and then only the cloud LLM - GPT4o, Claude, etc - is able to see your information.\",\n  },\n  {\n    question: 'Is Highlight SOC-2 compliant?',\n    answer:\n      'We are undergoing the SOC-2 process and expect to complete certification by end of 2025. In the meantime, we can supply and complete any security questionnaires for enterprises interested in working with us, and have published our policies on trust and safety here.',\n  },\n  {\n    question: 'How is this different from ChatGPT?',\n    answer:\n      \"Highlight is ChatGPT-level intelligence, but with the ability to understand what you're looking at on your screen or have said or heard on your laptop. Think of it like ChatGPT, but without the need to ever explain yourself (plus a bunch of other cool features).\",\n  },\n  {\n    question: 'Do you have a mobile app?',\n    answer: \"Not yet, but we're working on it!\",\n  },\n];\n\nexport default function FaqsHome() {\n  return (\n    <section className=\"mb-40\">\n      <div className=\"mx-auto max-w-6xl px-0 md:px-10 2xl:max-w-7xl\">\n        <h2\n          className=\"mx-auto mb-14 max-w-[320px] whitespace-pre-wrap text-center font-aeonik-bold font-medium text-3xl leading-tight sm:max-w-2xl sm:text-4xl md:max-w-none md:text-4xl lg:text-5xl\"\n          id=\"faq-heading\"\n        >\n          Common Questions\n        </h2>\n        <div className=\"h-full pb-8\">\n          <Accordion collapsible type=\"single\">\n            {faqs.map((faq, index) => (\n              <AccordionItem key={index} value={`item-${index}`}>\n                <AccordionTrigger className=\"cursor-pointer font-medium text-base text-primary hover:text-primary/80 hover:no-underline\">\n                  {faq.question}\n                </AccordionTrigger>\n                <AccordionContent className=\"font-aeonik text-lg text-muted-foreground\">\n                  {faq.answer}\n                </AccordionContent>\n              </AccordionItem>\n            ))}\n          </Accordion>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAOA,MAAM,OAAO;IACX;QACE,UAAU;QACV,QACE;IACJ;IACA;QACE,UAAU;QACV,QACE;IACJ;IACA;QACE,UAAU;QACV,QACE;IACJ;IACA;QACE,UAAU;QACV,QACE;IACJ;IACA;QACE,UAAU;QACV,QAAQ;IACV;CACD;AAEc,SAAS;IACtB,qBACE,6WAAC;QAAQ,WAAU;kBACjB,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBACC,WAAU;oBACV,IAAG;8BACJ;;;;;;8BAGD,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC,2IAAS;wBAAC,WAAW;wBAAC,MAAK;kCACzB,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6WAAC,+IAAa;gCAAa,OAAO,CAAC,KAAK,EAAE,OAAO;;kDAC/C,6WAAC,kJAAgB;wCAAC,WAAU;kDACzB,IAAI,QAAQ;;;;;;kDAEf,6WAAC,kJAAgB;wCAAC,WAAU;kDACzB,IAAI,MAAM;;;;;;;+BALK;;;;;;;;;;;;;;;;;;;;;;;;;;AAclC", "debugId": null}}, {"offset": {"line": 1115, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["export default function Featurea({\n  title,\n  description,\n  videoSrc,\n}: {\n  title: string;\n  description: string;\n  videoSrc: string;\n}) {\n  return (\n    <section className=\"mb-40\">\n      <div className=\"mx-auto max-w-6xl px-0 md:px-10 2xl:max-w-7xl\">\n        {/* Desktop */}\n        <h2\n          className=\"mb-6 text-center font-aeonik font-medium text-3xl leading-tight sm:text-4xl md:text-4xl lg:text-5xl\"\n          id=\"feature-title\"\n        >\n          <span className=\"mx-auto block max-w-[320px] sm:max-w-2xl md:max-w-4xl\">\n            {title}\n          </span>\n        </h2>\n        <div className=\"mb-12 text-center md:mb-16\">\n          <p className=\"mx-auto mb-8 max-w-[280px] font-aeonik text-lg text-muted-foreground sm:max-w-2xl sm:text-xl md:max-w-3xl\">\n            {description}\n          </p>\n        </div>\n\n        <div className=\"relative flex aspect-square w-full items-center justify-center overflow-hidden rounded-[20px] bg-[#303030] md:aspect-auto\">\n          <video\n            aria-label=\"Demo video 1\"\n            autoPlay\n            className=\"h-full w-full scale-125 rounded-[20px] object-cover md:h-auto md:scale-110 md:object-contain\"\n            loop\n            muted\n            playsInline\n            src={videoSrc}\n          >\n            <track kind=\"captions\" label=\"English captions\" srcLang=\"en\" />\n          </video>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS,SAAS,EAC/B,KAAK,EACL,WAAW,EACX,QAAQ,EAKT;IACC,qBACE,6WAAC;QAAQ,WAAU;kBACjB,cAAA,6WAAC;YAAI,WAAU;;8BAEb,6WAAC;oBACC,WAAU;oBACV,IAAG;8BAEH,cAAA,6WAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;8BAGL,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;8BAIL,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBACC,cAAW;wBACX,QAAQ;wBACR,WAAU;wBACV,IAAI;wBACJ,KAAK;wBACL,WAAW;wBACX,KAAK;kCAEL,cAAA,6WAAC;4BAAM,MAAK;4BAAW,OAAM;4BAAmB,SAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpE", "debugId": null}}, {"offset": {"line": 1203, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["/** biome-ignore-all lint/performance/noImgElement: <explanation> */\nexport default function FeaturesHome() {\n  return (\n    <section className=\"mb-40\">\n      <div className=\"mx-auto max-w-6xl px-0 md:px-10 2xl:max-w-7xl\">\n        <div className=\"flex flex-col items-center gap-[80px] font-aeonik\">\n          {/* Desktop */}\n          <div className=\"flex w-full flex-col items-start gap-[60px]\">\n            <div className=\"flex w-full flex-col items-start gap-5\">\n              <div className=\"font-medium text-brand-500 text-xl\">\n                Desktop Intelligence\n              </div>\n              <h2 className=\"max-w-[320px] font-aeonik font-medium text-3xl text-white/80 leading-none tracking-[-1px] sm:max-w-2xl md:max-w-none md:text-[42px]\">\n                <PERSON><PERSON> understands what you see and hear.\n              </h2>\n            </div>\n            <div className=\"relative flex aspect-square w-full items-center justify-center overflow-hidden rounded-[20px] bg-[#303030] md:aspect-auto\">\n              <video\n                aria-label=\"Demo video 1\"\n                autoPlay\n                className=\"h-full w-full scale-125 rounded-[20px] object-cover md:h-auto md:scale-110 md:object-contain\"\n                loop\n                muted\n                playsInline\n                src=\"https://cdn.highlightai.com/media/landing/misc/hero_demo.webm\"\n              >\n                <track kind=\"captions\" label=\"English captions\" srcLang=\"en\" />\n              </video>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": "AAAA,kEAAkE;;;;;;AACnD,SAAS;IACtB,qBACE,6WAAC;QAAQ,WAAU;kBACjB,cAAA,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;0BAEb,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAI,WAAU;8CAAqC;;;;;;8CAGpD,6WAAC;oCAAG,WAAU;8CAAsI;;;;;;;;;;;;sCAItJ,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC;gCACC,cAAW;gCACX,QAAQ;gCACR,WAAU;gCACV,IAAI;gCACJ,KAAK;gCACL,WAAW;gCACX,KAAI;0CAEJ,cAAA,6WAAC;oCAAM,MAAK;oCAAW,OAAM;oCAAmB,SAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxE", "debugId": null}}, {"offset": {"line": 1299, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/infinite-slider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const InfiniteSlider = registerClientReference(\n    function() { throw new Error(\"Attempted to call InfiniteSlider() from the server but InfiniteSlider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/infinite-slider.tsx <module evaluation>\",\n    \"InfiniteSlider\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,iBAAiB,IAAA,uYAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,mEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1313, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/infinite-slider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const InfiniteSlider = registerClientReference(\n    function() { throw new Error(\"Attempted to call InfiniteSlider() from the server but InfiniteSlider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/infinite-slider.tsx\",\n    \"InfiniteSlider\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,iBAAiB,IAAA,uYAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1327, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/progressive-blur.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GRADIENT_ANGLES = registerClientReference(\n    function() { throw new Error(\"Attempted to call G<PERSON><PERSON>ENT_ANGLES() from the server but <PERSON><PERSON><PERSON>ENT_ANGLES is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/progressive-blur.tsx <module evaluation>\",\n    \"GRADIENT_ANGLES\",\n);\nexport const ProgressiveBlur = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProgressiveBlur() from the server but ProgressiveBlur is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/progressive-blur.tsx <module evaluation>\",\n    \"ProgressiveBlur\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;AACvE;;AACO,MAAM,kBAAkB,IAAA,uYAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,oEACA;AAEG,MAAM,kBAAkB,IAAA,uYAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/progressive-blur.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GRADIENT_ANGLES = registerClientReference(\n    function() { throw new Error(\"Attempted to call G<PERSON><PERSON>ENT_ANGLES() from the server but <PERSON><PERSON><PERSON>ENT_ANGLES is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/progressive-blur.tsx\",\n    \"GRADIENT_ANGLES\",\n);\nexport const ProgressiveBlur = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProgressiveBlur() from the server but ProgressiveBlur is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/progressive-blur.tsx\",\n    \"ProgressiveBlur\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;AACvE;;AACO,MAAM,kBAAkB,IAAA,uYAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,gDACA;AAEG,MAAM,kBAAkB,IAAA,uYAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,gDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1373, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["/** biome-ignore-all lint/performance/noImgElement: <explanation> */\nimport { InfiniteSlider } from '@/components/ui/infinite-slider';\nimport { ProgressiveBlur } from '@/components/ui/progressive-blur';\n\nconst logos = [\n  {\n    src: 'https://html.tailus.io/blocks/customers/nvidia.svg',\n    alt: 'Nvidia Logo',\n  },\n  {\n    src: 'https://html.tailus.io/blocks/customers/column.svg',\n    alt: 'Column Logo',\n  },\n  {\n    src: 'https://html.tailus.io/blocks/customers/github.svg',\n    alt: 'GitHub Logo',\n  },\n  {\n    src: 'https://html.tailus.io/blocks/customers/nike.svg',\n    alt: 'Nike Logo',\n  },\n  {\n    src: 'https://html.tailus.io/blocks/customers/lemonsqueezy.svg',\n    alt: 'Lemon Squeezy Logo',\n  },\n  {\n    src: 'https://html.tailus.io/blocks/customers/laravel.svg',\n    alt: 'Laravel Logo',\n  },\n  {\n    src: 'https://html.tailus.io/blocks/customers/lilly.svg',\n    alt: 'Lilly Logo',\n  },\n  {\n    src: 'https://html.tailus.io/blocks/customers/openai.svg',\n    alt: 'OpenAI Logo',\n  },\n];\nexport default function LogoCloud() {\n  return (\n    <section className=\"overflow-hidden bg-background py-10\">\n      <div className=\"group relative m-auto max-w-7xl\">\n        <div className=\"relative\">\n          <InfiniteSlider gap={112} speed={40} speedOnHover={20}>\n            {logos.map((logo) => (\n              <div className=\"flex\" key={logo.alt}>\n                <img\n                  alt={logo.alt}\n                  className=\"mx-auto h-5 w-fit invert-75\"\n                  height=\"20\"\n                  src={logo.src}\n                  width=\"auto\"\n                />\n              </div>\n            ))}\n          </InfiniteSlider>\n\n          <div className=\"absolute inset-y-0 left-0 w-20 bg-linear-to-r from-background\" />\n          <div className=\"absolute inset-y-0 right-0 w-20 bg-linear-to-l from-background\" />\n          <ProgressiveBlur\n            blurIntensity={1}\n            className=\"pointer-events-none absolute top-0 left-0 h-full w-20\"\n            direction=\"left\"\n          />\n          <ProgressiveBlur\n            blurIntensity={1}\n            className=\"pointer-events-none absolute top-0 right-0 h-full w-20\"\n            direction=\"right\"\n          />\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": "AAAA,kEAAkE;;;;;AAClE;AACA;;;;AAEA,MAAM,QAAQ;IACZ;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;CACD;AACc,SAAS;IACtB,qBACE,6WAAC;QAAQ,WAAU;kBACjB,cAAA,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,yJAAc;wBAAC,KAAK;wBAAK,OAAO;wBAAI,cAAc;kCAChD,MAAM,GAAG,CAAC,CAAC,qBACV,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCACC,KAAK,KAAK,GAAG;oCACb,WAAU;oCACV,QAAO;oCACP,KAAK,KAAK,GAAG;oCACb,OAAM;;;;;;+BANiB,KAAK,GAAG;;;;;;;;;;kCAYvC,6WAAC;wBAAI,WAAU;;;;;;kCACf,6WAAC;wBAAI,WAAU;;;;;;kCACf,6WAAC,2JAAe;wBACd,eAAe;wBACf,WAAU;wBACV,WAAU;;;;;;kCAEZ,6WAAC,2JAAe;wBACd,eAAe;wBACf,WAAU;wBACV,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMtB", "debugId": null}}, {"offset": {"line": 1513, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["import { Button } from '@/components/ui/button';\nimport LogoCloud from './logo-cloud';\n\nexport default function HeroHome() {\n  return (\n    <section\n      aria-labelledby=\"hero-section\"\n      className=\"relative mt-10 mb-[160px] flex h-full flex-col justify-center sm:h-full lg:mt-20\"\n    >\n      <div className=\"grid grid-cols-1 items-center gap-8 py-8 sm:py-0 lg:grid-cols-1\">\n        <div className=\"flex flex-col items-center justify-center py-8 text-center sm:min-h-[280px] lg:text-left\">\n          <div className=\"flex w-full flex-col\">\n            <h1 className=\"flex flex-col items-center\">\n              <div className=\"mx-auto inline-block max-w-[440px] text-pretty break-words text-center font-aeonik font-medium text-4xl text-white/80 tracking-none md:max-w-[600px] md:text-5xl md:leading-[1.05em] lg:max-w-[780px] lg:text-[65px]\">\n                The AI Assistant that works everywhere you do.\n              </div>\n            </h1>\n          </div>\n          <p className=\"mt-4 max-w-2xl whitespace-pre-line text-center font-aeonik font-normal text-lg text-muted-foreground sm:mt-8 md:text-xl\">\n            Highlight keeps track of your meetings, chats, and tasks so you can\n            find answers, create, and act, all in one place, personalized for\n            you.\n          </p>\n          <div className=\"mt-6 flex items-center gap-6 lg:mt-8 xl:mt-10\">\n            <Button\n              className=\"rounded-full bg-brand-500 hover:bg-brand-400\"\n              size={'lg'}\n            >\n              Book Demo\n            </Button>\n            <Button\n              className=\"rounded-full bg-brand-500 hover:bg-brand-400\"\n              size={'lg'}\n            >\n              Learn more\n            </Button>\n          </div>\n        </div>\n        <div className=\"flex flex-col gap-12 md:flex-col lg:gap-16\">\n          {/* companies we work with */}\n          <section className=\"flex flex-col items-center gap-4\">\n            <div className=\"flex flex-col items-center font-aeonik\">\n              <h2 className=\"max-w-[200px] text-center font-medium text-md text-muted-foreground/50 sm:max-w-none sm:text-lg\">\n                Loved by 100,000+ users and teams worldwide!\n              </h2>\n            </div>\n            <LogoCloud />\n          </section>\n          {/* hero image */}\n          <section className=\"aspect-[1/1] w-full sm:aspect-[16/9] md:p-8\">\n            <div className=\"relative h-full w-full overflow-hidden rounded-[20px] bg-[#0D0D0D] md:rounded-[30px]\">\n              <video\n                aria-label=\"Demo video 1\"\n                autoPlay\n                className=\"absolute inset-0 h-full w-full object-cover\"\n                loop\n                muted\n                playsInline\n                src=\"https://cdn.highlightai.com/media/landing/misc/hero_demo.webm\"\n              >\n                <track kind=\"captions\" label=\"English captions\" srcLang=\"en\" />\n              </video>\n            </div>\n          </section>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,6WAAC;QACC,mBAAgB;QAChB,WAAU;kBAEV,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC;gCAAG,WAAU;0CACZ,cAAA,6WAAC;oCAAI,WAAU;8CAAuN;;;;;;;;;;;;;;;;sCAK1O,6WAAC;4BAAE,WAAU;sCAA0H;;;;;;sCAKvI,6WAAC;4BAAI,WAAU;;8CACb,6WAAC,qIAAM;oCACL,WAAU;oCACV,MAAM;8CACP;;;;;;8CAGD,6WAAC,qIAAM;oCACL,WAAU;oCACV,MAAM;8CACP;;;;;;;;;;;;;;;;;;8BAKL,6WAAC;oBAAI,WAAU;;sCAEb,6WAAC;4BAAQ,WAAU;;8CACjB,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC;wCAAG,WAAU;kDAAkG;;;;;;;;;;;8CAIlH,6WAAC,6IAAS;;;;;;;;;;;sCAGZ,6WAAC;4BAAQ,WAAU;sCACjB,cAAA,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCACC,cAAW;oCACX,QAAQ;oCACR,WAAU;oCACV,IAAI;oCACJ,KAAK;oCACL,WAAW;oCACX,KAAI;8CAEJ,cAAA,6WAAC;wCAAM,MAAK;wCAAW,OAAM;wCAAmB,SAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxE", "debugId": null}}, {"offset": {"line": 1686, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["import {\n  ArrowUp<PERSON>ightI<PERSON>,\n  LockKeyholeIcon,\n  ShieldCheckIcon,\n  ShieldUserIcon,\n} from 'lucide-react';\n\nconst privacyData = [\n  {\n    icon: LockKeyholeIcon,\n    title: 'Encrypted',\n    description:\n      'Your messages and chats are fully encrypted during transit and at rest.',\n  },\n  {\n    icon: ShieldCheckIcon,\n    title: 'Enterprise-Grade',\n    description:\n      'Our infrastructure is built to the highest standards of reliability and security.',\n  },\n  {\n    icon: ShieldUserIcon,\n    title: 'Privacy-First',\n    description:\n      'We will never train on or sell your data - your information stays with you.',\n  },\n];\nexport default function Privacy() {\n  return (\n    <section className=\"mb-40\">\n      <div className=\"flex h-auto flex-col items-center justify-center gap-8 rounded-[20px] p-8 font-aeonik\">\n        <h2 className=\"mx-auto max-w-[320px] whitespace-pre-wrap text-center font-medium text-3xl leading-tight sm:max-w-2xl sm:text-4xl md:max-w-none md:text-4xl lg:text-5xl\">\n          We're serious about privacy.\n        </h2>\n        <p className=\"mx-auto w-full max-w-[280px] text-center font-normal text-lg text-muted-foreground sm:max-w-xl sm:text-xl\">\n          Highlight is built with privacy at its core. Experience powerful AI\n          assistance without compromising on your privacy.\n        </p>\n        <a\n          className=\"group font-medium text-brand-500/75 transition-all duration-300 hover:text-brand-500\"\n          href=\"https://docs.highlightai.com/privacy\"\n          rel=\"noopener\"\n          target=\"_blank\"\n        >\n          <span className=\"group-hover:-translate-x-2 inline-flex translate-x-2 items-center transition-transform duration-300\">\n            Our Privacy Policy\n            <ArrowUpRightIcon className=\"lucide lucide-arrow-up-right ml-1 h-4 w-4 origin-left scale-0 transition-all duration-300 group-hover:scale-100\" />\n          </span>\n        </a>\n        <div className=\"w-full max-w-4xl sm:p-8\">\n          <div className=\"grid grid-cols-1 gap-6 md:grid-cols-3\">\n            {privacyData.map((item) => (\n              <div\n                className=\"flex flex-shrink-0 flex-col justify-between rounded-xl border border-muted/5 bg-muted/20 p-6 px-7 shadow-sm\"\n                key={item.title}\n              >\n                <div className=\"relative z-10 mb-8\">\n                  <item.icon className=\"size-11 text-muted-foreground\" />\n                </div>\n                <span>\n                  <h3 className=\"mb-2 font-medium font-sans text-white text-xl\">\n                    {item.title}\n                  </h3>\n                  <p className=\"w-full max-w-lg text-start font-normal text-md text-muted-foreground\">\n                    {item.description}\n                  </p>\n                </span>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;;;AAOA,MAAM,cAAc;IAClB;QACE,MAAM,+TAAe;QACrB,OAAO;QACP,aACE;IACJ;IACA;QACE,MAAM,+TAAe;QACrB,OAAO;QACP,aACE;IACJ;IACA;QACE,MAAM,4TAAc;QACpB,OAAO;QACP,aACE;IACJ;CACD;AACc,SAAS;IACtB,qBACE,6WAAC;QAAQ,WAAU;kBACjB,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAG,WAAU;8BAA0J;;;;;;8BAGxK,6WAAC;oBAAE,WAAU;8BAA4G;;;;;;8BAIzH,6WAAC;oBACC,WAAU;oBACV,MAAK;oBACL,KAAI;oBACJ,QAAO;8BAEP,cAAA,6WAAC;wBAAK,WAAU;;4BAAsG;0CAEpH,6WAAC,sUAAgB;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAGhC,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,6WAAC;gCACC,WAAU;;kDAGV,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6WAAC;;0DACC,6WAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;0DAEb,6WAAC;gDAAE,WAAU;0DACV,KAAK,WAAW;;;;;;;;;;;;;+BAVhB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;AAoB/B", "debugId": null}}, {"offset": {"line": 1840, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/magicui/marquee.tsx"], "sourcesContent": ["import type { ComponentPropsWithoutRef } from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface MarqueeProps extends ComponentPropsWithoutRef<'div'> {\n  /**\n   * Optional CSS class name to apply custom styles\n   */\n  className?: string;\n  /**\n   * Whether to reverse the animation direction\n   * @default false\n   */\n  reverse?: boolean;\n  /**\n   * Whether to pause the animation on hover\n   * @default false\n   */\n  pauseOnHover?: boolean;\n  /**\n   * Content to be displayed in the marquee\n   */\n  children: React.ReactNode;\n  /**\n   * Whether to animate vertically instead of horizontally\n   * @default false\n   */\n  vertical?: boolean;\n  /**\n   * Number of times to repeat the content\n   * @default 4\n   */\n  repeat?: number;\n}\n\nexport function Marquee({\n  className,\n  reverse = false,\n  pauseOnHover = false,\n  children,\n  vertical = false,\n  repeat = 4,\n  ...props\n}: MarqueeProps) {\n  return (\n    <div\n      {...props}\n      className={cn(\n        'group flex overflow-hidden p-2 [--duration:40s] [--gap:1rem] [gap:var(--gap)]',\n        {\n          'flex-row': !vertical,\n          'flex-col': vertical,\n        },\n        className\n      )}\n    >\n      {Array(repeat)\n        .fill(0)\n        .map((_, i) => (\n          <div\n            className={cn('flex shrink-0 justify-around [gap:var(--gap)]', {\n              'animate-marquee flex-row': !vertical,\n              'animate-marquee-vertical flex-col': vertical,\n              'group-hover:[animation-play-state:paused]': pauseOnHover,\n              '[animation-direction:reverse]': reverse,\n            })}\n            key={i}\n          >\n            {children}\n          </div>\n        ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;;;AAiCO,SAAS,QAAQ,EACtB,SAAS,EACT,UAAU,KAAK,EACf,eAAe,KAAK,EACpB,QAAQ,EACR,WAAW,KAAK,EAChB,SAAS,CAAC,EACV,GAAG,OACU;IACb,qBACE,6WAAC;QACE,GAAG,KAAK;QACT,WAAW,IAAA,kHAAE,EACX,iFACA;YACE,YAAY,CAAC;YACb,YAAY;QACd,GACA;kBAGD,MAAM,QACJ,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,6WAAC;gBACC,WAAW,IAAA,kHAAE,EAAC,iDAAiD;oBAC7D,4BAA4B,CAAC;oBAC7B,qCAAqC;oBACrC,6CAA6C;oBAC7C,iCAAiC;gBACnC;0BAGC;eAFI;;;;;;;;;;AAOjB", "debugId": null}}, {"offset": {"line": 1878, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["import Image from 'next/image';\nimport { cn } from '@/lib/utils';\n\nexport const ReviewCard = ({\n  img,\n  name,\n  username,\n  body,\n}: {\n  img: string;\n  name: string;\n  username: string;\n  body: string;\n}) => {\n  return (\n    <figure\n      className={cn(\n        \"group flex max-h-72 w-fit flex-shrink-0 flex-col justify-between gap-8 rounded-xl bg-muted/40 p-4 px-5 opacity-60 shadow-sm transition-all duration-300 hover:bg-[url('/gradient.webp')] hover:bg-center hover:bg-cover hover:bg-no-repeat hover:opacity-100 hover:shadow-md md:w-[28rem] md:p-6 md:px-7 [&:hover_.avatar-ring]:ring-brandYellow [&:hover_.external-link-icon]:opacity-100 [&:hover_.social-icon-border]:border-brand-600\"\n      )}\n    >\n      <blockquote className=\"mt-2 font-medium text-md text-primary tracking-normal\">\n        {body}\n        One of the most delightful, inventive, powerful new interfaces I've\n        tried in years. Actually feels like an AI native computer.\n      </blockquote>\n      <div className=\"relative flex w-full items-center gap-3 text-left transition-all duration-300 hover:scale-[98%]\">\n        <Image\n          alt=\"\"\n          className=\"size-12 rounded-full\"\n          height=\"48\"\n          src={img}\n          width=\"48\"\n        />\n\n        <div className=\"flex-1\">\n          <figcaption className=\"font-aeonik font-medium text-lg text-primary/80 tracking-normal\">\n            {name}\n          </figcaption>\n          <p className=\"font-aeonik font-normal text-md text-primary/50 tracking-tight\">\n            {username}\n          </p>\n        </div>\n      </div>\n    </figure>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEO,MAAM,aAAa,CAAC,EACzB,GAAG,EACH,IAAI,EACJ,QAAQ,EACR,IAAI,EAML;IACC,qBACE,6WAAC;QACC,WAAW,IAAA,kHAAE,EACX;;0BAGF,6WAAC;gBAAW,WAAU;;oBACnB;oBAAK;;;;;;;0BAIR,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,uQAAK;wBACJ,KAAI;wBACJ,WAAU;wBACV,QAAO;wBACP,KAAK;wBACL,OAAM;;;;;;kCAGR,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAW,WAAU;0CACnB;;;;;;0CAEH,6WAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1959, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["import { Marquee } from '@/components/magicui/marquee';\nimport { reviews } from '@/config/docs';\nimport { ReviewCard } from './review-card';\n\nconst firstRow = reviews.slice(0, reviews.length / 2);\nconst secondRow = reviews.slice(reviews.length / 2);\n\nexport function ReviewCards() {\n  return (\n    <div className=\"relative hidden w-full flex-col items-center justify-center overflow-hidden md:flex\">\n      <Marquee className=\"[--duration:60s]\" pauseOnHover>\n        {firstRow.map((review) => (\n          <ReviewCard key={review.name} {...review} />\n        ))}\n      </Marquee>\n      <Marquee className=\"[--duration:60s]\" pauseOnHover reverse>\n        {secondRow.map((review) => (\n          <ReviewCard key={review.name} {...review} />\n        ))}\n      </Marquee>\n      <div className=\"pointer-events-none absolute inset-y-0 left-0 w-1/4 bg-gradient-to-r from-background\" />\n      <div className=\"pointer-events-none absolute inset-y-0 right-0 w-1/4 bg-gradient-to-l from-background\" />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,WAAW,yHAAO,CAAC,KAAK,CAAC,GAAG,yHAAO,CAAC,MAAM,GAAG;AACnD,MAAM,YAAY,yHAAO,CAAC,KAAK,CAAC,yHAAO,CAAC,MAAM,GAAG;AAE1C,SAAS;IACd,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC,4IAAO;gBAAC,WAAU;gBAAmB,YAAY;0BAC/C,SAAS,GAAG,CAAC,CAAC,uBACb,6WAAC,iJAAU;wBAAoB,GAAG,MAAM;uBAAvB,OAAO,IAAI;;;;;;;;;;0BAGhC,6WAAC,4IAAO;gBAAC,WAAU;gBAAmB,YAAY;gBAAC,OAAO;0BACvD,UAAU,GAAG,CAAC,CAAC,uBACd,6WAAC,iJAAU;wBAAoB,GAAG,MAAM;uBAAvB,OAAO,IAAI;;;;;;;;;;0BAGhC,6WAAC;gBAAI,WAAU;;;;;;0BACf,6WAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 2033, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/features/home/<USER>'s on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/features/home/<USER>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,uYAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAyS,GACtU,uEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2047, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/features/home/<USER>'s on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/features/home/<USER>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,uYAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAqR,GAClT,mDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2061, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2069, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["import { ArrowUpRightIcon } from 'lucide-react';\nimport { ReviewCards } from './review-cards';\nimport ReviewCardsMobile from './review-cards-mobile';\n\nexport default function Testimonials() {\n  return (\n    <section className=\"mb-14\">\n      <div className=\"mx-auto mb-14 max-w-6xl px-0 md:px-10 2xl:max-w-7xl\">\n        <h2 className=\"mx-auto mb-6 max-w-[320px] whitespace-pre-wrap text-center font-aeonik-bold font-medium text-3xl leading-tight sm:max-w-2xl sm:text-4xl md:max-w-none md:text-4xl lg:text-5xl\">\n          Hear from our users\n        </h2>\n        <p className=\"mx-auto mb-8 max-w-[280px] text-center font-aeonik text-lg text-muted-foreground sm:max-w-xl sm:text-xl\">\n          Highlight is loved by Mac and Windows users around the world. Become a\n          part of our community today by joining our Discord!\n        </p>\n        <div className=\"flex justify-center\">\n          <a\n            className=\"group font-medium font-sans text-brand-600 transition-all duration-300 hover:text-brand-600/80\"\n            href=\"/discord\"\n            rel=\"noopener noreferrer\"\n            target=\"_blank\"\n          >\n            <span className=\"group-hover:-translate-x-2 inline-flex translate-x-2 items-center transition-transform duration-300\">\n              Join our Community\n              <ArrowUpRightIcon className=\"lucide lucide-arrow-up-right ml-1 h-4 w-4 origin-left scale-0 transition-all duration-300 group-hover:scale-100\" />\n            </span>\n          </a>\n        </div>\n      </div>\n      <ReviewCards />\n      <ReviewCardsMobile />\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEe,SAAS;IACtB,qBACE,6WAAC;QAAQ,WAAU;;0BACjB,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;kCAAgL;;;;;;kCAG9L,6WAAC;wBAAE,WAAU;kCAA0G;;;;;;kCAIvH,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BACC,WAAU;4BACV,MAAK;4BACL,KAAI;4BACJ,QAAO;sCAEP,cAAA,6WAAC;gCAAK,WAAU;;oCAAsG;kDAEpH,6WAAC,sUAAgB;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAKpC,6WAAC,mJAAW;;;;;0BACZ,6WAAC,yJAAiB;;;;;;;;;;;AAGxB", "debugId": null}}, {"offset": {"line": 2165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/app/%28root%29/page.tsx"], "sourcesContent": ["import Comparison from '@/features/home/<USER>';\nimport EditorFeatureHome from '@/features/home/<USER>';\nimport FaqsHome from '@/features/home/<USER>';\nimport Featurea from '@/features/home/<USER>';\nimport FeaturesHome from '@/features/home/<USER>';\nimport HeroHome from '@/features/home/<USER>';\nimport Privacy from '@/features/home/<USER>';\nimport Testimonials from '@/features/home/<USER>';\n\nexport default function Home() {\n  return (\n    <main className=\"pt-[90px]\">\n      <HeroHome />\n      <EditorFeatureHome />\n      <FeaturesHome />\n      <Featurea\n        description=\"Generate PRDs, emails, and research briefs—all powered by the context from your day.\"\n        title=\"Go from summary to action in seconds.\"\n        videoSrc=\"https://cdn.highlightai.com/media/landing/misc/hero_demo.webm\"\n      />\n      <Featurea\n        description=\"Work across weeks of insights, not just today's notes. Spot patterns in customer feedback, track decision evolution, and surface trends that span multiple meetings.\"\n        title=\"Query weeks of insights instantly.\"\n        videoSrc=\"https://cdn.highlightai.com/media/landing/misc/hero_demo.webm\"\n      />\n      <Privacy />\n      <Comparison />\n      <FaqsHome />\n      <Testimonials />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEe,SAAS;IACtB,qBACE,6WAAC;QAAK,WAAU;;0BACd,6WAAC,oIAAQ;;;;;0BACT,6WAAC,sIAAiB;;;;;0BAClB,6WAAC,wIAAY;;;;;0BACb,6WAAC,wIAAQ;gBACP,aAAY;gBACZ,OAAM;gBACN,UAAS;;;;;;0BAEX,6WAAC,wIAAQ;gBACP,aAAY;gBACZ,OAAM;gBACN,UAAS;;;;;;0BAEX,6WAAC,uIAAO;;;;;0BACR,6WAAC,0IAAU;;;;;0BACX,6WAAC,oIAAQ;;;;;0BACT,6WAAC,4IAAY;;;;;;;;;;;AAGnB", "debugId": null}}]}