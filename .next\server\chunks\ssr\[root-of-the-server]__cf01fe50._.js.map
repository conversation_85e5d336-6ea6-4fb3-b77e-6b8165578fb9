{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/emails/subscribe-template.tsx"], "sourcesContent": ["import {\r\n  <PERSON>,\r\n  <PERSON><PERSON>,\r\n  Column,\r\n  Con<PERSON>er,\r\n  <PERSON><PERSON>,\r\n  <PERSON>,\r\n  Hr,\r\n  Html,\r\n  Link,\r\n  Preview,\r\n  Row,\r\n  Section,\r\n  Tailwind,\r\n  Text,\r\n} from '@react-email/components';\r\n\r\ntype EmailTemplateProps= {\r\n  email: string;\r\n}\r\n\r\nexport const SubscribedTemplate = ({ email }: EmailTemplateProps) => {\r\n  const currentDate = new Date().toLocaleDateString('en-US', {\r\n    year: 'numeric',\r\n    month: 'long',\r\n    day: 'numeric',\r\n    hour: '2-digit',\r\n    minute: '2-digit',\r\n  });\r\n\r\n  // Ensure the email is displayed safely\r\n  const safeEmail = email || '[No email provided]';\r\n\r\n  return (\r\n    <Html>\r\n      <Head>\r\n        <Font\r\n          fallbackFontFamily=\"Arial\"\r\n          fontFamily=\"Roboto\"\r\n          fontStyle=\"normal\"\r\n          fontWeight={400}\r\n          webFont={{\r\n            url: 'https://fonts.gstatic.com/s/roboto/v27/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2',\r\n            format: 'woff2',\r\n          }}\r\n        />\r\n      </Head>\r\n      <Preview>\r\n        New subscriber: {safeEmail} has joined your mailing list!\r\n      </Preview>\r\n      <Tailwind>\r\n        <Body className=\"mx-auto my-auto bg-gray-50 font-sans\">\r\n          <Container className=\"mx-auto my-8 max-w-[600px]\">\r\n            {/* Header */}\r\n            <Section className=\"rounded-t-lg bg-blue-600 px-8 py-6\">\r\n              <Row>\r\n                <Column>\r\n                  <Text className=\"text-center font-bold text-3xl text-white\">\r\n                    Rathon\r\n                  </Text>\r\n                </Column>\r\n              </Row>\r\n            </Section>\r\n\r\n            {/* Main Content */}\r\n            <Section className=\"rounded-b-lg bg-white px-8 py-10 shadow-sm\">\r\n              <Row>\r\n                <Column>\r\n                  <Text className=\"mb-5 font-bold text-2xl text-gray-800\">\r\n                    New Subscriber Alert\r\n                  </Text>\r\n\r\n                  <Text className=\"mb-5 text-gray-700\">\r\n                    You have a new subscriber to your mailing list! Someone is\r\n                    interested in hearing more about Rathon&apos;s services and\r\n                    updates.\r\n                  </Text>\r\n\r\n                  {/* Subscriber Info */}\r\n                  <Container className=\"mb-6 rounded-md border-blue-500 border-l-4 bg-blue-50 p-5\">\r\n                    <Text className=\"mb-1 font-medium text-base text-blue-800\">\r\n                      Subscriber Details:\r\n                    </Text>\r\n                    <Text className=\"mb-0 text-blue-800\">\r\n                      <strong>Email:</strong> {safeEmail}\r\n                    </Text>\r\n                    <Text className=\"mb-0 text-blue-800\">\r\n                      <strong>Subscribed on:</strong> {currentDate}\r\n                    </Text>\r\n                  </Container>\r\n\r\n                  {/* Action Button */}\r\n                  <Section className=\"mb-8 text-center\">\r\n                    <Button\r\n                      className=\"rounded-md bg-blue-600 px-6 py-3 font-medium text-white hover:bg-blue-700\"\r\n                      href={`mailto:${safeEmail}`}\r\n                    >\r\n                      Contact Subscriber\r\n                    </Button>\r\n                  </Section>\r\n\r\n                  <Hr className=\"my-6 border-gray-200\" />\r\n\r\n                  <Text className=\"mb-4 text-gray-700\">\r\n                    Remember to maintain GDPR compliance when reaching out to\r\n                    new subscribers.\r\n                  </Text>\r\n                </Column>\r\n              </Row>\r\n            </Section>\r\n\r\n            {/* Footer */}\r\n            <Section className=\"px-8 py-6\">\r\n              <Text className=\"text-center text-gray-500 text-xs\">\r\n                © {new Date().getFullYear()} Rathon. All rights reserved.\r\n              </Text>\r\n              <Text className=\"text-center text-gray-500 text-xs\">\r\n                This is an automated notification from your website&apos;s\r\n                subscription system.\r\n              </Text>\r\n              <Text className=\"text-center text-gray-500 text-xs\">\r\n                <Link className=\"text-blue-500 underline\" href=\"#\">\r\n                  Unsubscribe\r\n                </Link>{' '}\r\n                from these notifications\r\n              </Text>\r\n            </Section>\r\n          </Container>\r\n        </Body>\r\n      </Tailwind>\r\n    </Html>\r\n  );\r\n};"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAqBO,MAAM,qBAAqB,CAAC,EAAE,KAAK,EAAsB;IAC9D,MAAM,cAAc,IAAI,OAAO,kBAAkB,CAAC,SAAS;QACzD,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;IAEA,uCAAuC;IACvC,MAAM,YAAY,SAAS;IAE3B,qBACE,6WAAC,8PAAI;;0BACH,6WAAC,8PAAI;0BACH,cAAA,6WAAC,6PAAI;oBACH,oBAAmB;oBACnB,YAAW;oBACX,WAAU;oBACV,YAAY;oBACZ,SAAS;wBACP,KAAK;wBACL,QAAQ;oBACV;;;;;;;;;;;0BAG<PERSON>,6WAAC,uQAAO;;oBAAC;oBACU;oBAAU;;;;;;;0BAE7B,6WAAC,yQAAQ;0BACP,cAAA,6WAAC,6PAAI;oBAAC,WAAU;8BACd,cAAA,6WAAC,6QAAS;wBAAC,WAAU;;0CAEnB,6WAAC,uQAAO;gCAAC,WAAU;0CACjB,cAAA,6WAAC,2PAAG;8CACF,cAAA,6WAAC,oQAAM;kDACL,cAAA,6WAAC,6PAAI;4CAAC,WAAU;sDAA4C;;;;;;;;;;;;;;;;;;;;;0CAQlE,6WAAC,uQAAO;gCAAC,WAAU;0CACjB,cAAA,6WAAC,2PAAG;8CACF,cAAA,6WAAC,oQAAM;;0DACL,6WAAC,6PAAI;gDAAC,WAAU;0DAAwC;;;;;;0DAIxD,6WAAC,6PAAI;gDAAC,WAAU;0DAAqB;;;;;;0DAOrC,6WAAC,6QAAS;gDAAC,WAAU;;kEACnB,6WAAC,6PAAI;wDAAC,WAAU;kEAA2C;;;;;;kEAG3D,6WAAC,6PAAI;wDAAC,WAAU;;0EACd,6WAAC;0EAAO;;;;;;4DAAe;4DAAE;;;;;;;kEAE3B,6WAAC,6PAAI;wDAAC,WAAU;;0EACd,6WAAC;0EAAO;;;;;;4DAAuB;4DAAE;;;;;;;;;;;;;0DAKrC,6WAAC,uQAAO;gDAAC,WAAU;0DACjB,cAAA,6WAAC,mQAAM;oDACL,WAAU;oDACV,MAAM,CAAC,OAAO,EAAE,WAAW;8DAC5B;;;;;;;;;;;0DAKH,6WAAC,wPAAE;gDAAC,WAAU;;;;;;0DAEd,6WAAC,6PAAI;gDAAC,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;;;;;0CAS3C,6WAAC,uQAAO;gCAAC,WAAU;;kDACjB,6WAAC,6PAAI;wCAAC,WAAU;;4CAAoC;4CAC/C,IAAI,OAAO,WAAW;4CAAG;;;;;;;kDAE9B,6WAAC,6PAAI;wCAAC,WAAU;kDAAoC;;;;;;kDAIpD,6WAAC,6PAAI;wCAAC,WAAU;;0DACd,6WAAC,8PAAI;gDAAC,WAAU;gDAA0B,MAAK;0DAAI;;;;;;4CAE3C;4CAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5B", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/server/subscribe.action.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { Resend } from \"resend\";\r\nimport { SubscribedTemplate } from \"@/components/emails/subscribe-template\";\r\nimport type { TSubFormSchema } from \"./schema\";\r\n\r\n// Initialize Resend client with API key from environment variables\r\nconst resendClient = new Resend(process.env.RESEND_API_KEY);\r\n\r\nexport async function subscribe(formData: TSubFormSchema) {\r\n  try {\r\n    const { email } = formData;\r\n\r\n    const response = await resendClient.emails.send({\r\n      from: \"Rathon <<EMAIL>>\",\r\n      to: [\"<EMAIL>\"],\r\n      subject: \"New Subscriber from Rathon Website\",\r\n      react: SubscribedTemplate({ email }) as React.ReactElement,\r\n      replyTo: email,\r\n      tags: [{ name: \"source\", value: \"website_subscribe\" }],\r\n    });\r\n\r\n    if (response.error) {\r\n      return { success: false, error: response.error };\r\n    }\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    return { success: false, error };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;;;AAGA,mEAAmE;AACnE,MAAM,eAAe,IAAI,8PAAM,CAAC,QAAQ,GAAG,CAAC,cAAc;AAEnD,eAAe,UAAU,QAAwB;IACtD,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG;QAElB,MAAM,WAAW,MAAM,aAAa,MAAM,CAAC,IAAI,CAAC;YAC9C,MAAM;YACN,IAAI;gBAAC;aAAqB;YAC1B,SAAS;YACT,OAAO,IAAA,oKAAkB,EAAC;gBAAE;YAAM;YAClC,SAAS;YACT,MAAM;gBAAC;oBAAE,MAAM;oBAAU,OAAO;gBAAoB;aAAE;QACxD;QAEA,IAAI,SAAS,KAAK,EAAE;YAClB,OAAO;gBAAE,SAAS;gBAAO,OAAO,SAAS,KAAK;YAAC;QACjD;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;;;IArBsB;;AAAA,8WAAA", "debugId": null}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/.next-internal/server/app/%28root%29/about/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {subscribe as '401553368a882499aede4e05ebfdb3ca7882231805'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/aspect-ratio.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AspectRatio = registerClientReference(\n    function() { throw new Error(\"Attempted to call AspectRatio() from the server but AspectRatio is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aspect-ratio.tsx <module evaluation>\",\n    \"AspectRatio\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,cAAc,IAAA,uYAAuB,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,gEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/aspect-ratio.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AspectRatio = registerClientReference(\n    function() { throw new Error(\"Attempted to call AspectRatio() from the server but AspectRatio is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aspect-ratio.tsx\",\n    \"AspectRatio\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,cAAc,IAAA,uYAAuB,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/public/team.webp.mjs%20%28structured%20image%20object%20with%20data%20url%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 2048, height: 1358, blurWidth: 8, blurHeight: 5, blurDataURL: \"data:image/webp;base64,UklGRtsAAABXRUJQVlA4TM8AAAAvBwABAM1VICICHgiACQMAAICyjdmFBKUAAAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAAAAeYUVoBAAB4IAA1EAAAAOd/daO3DQYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA86qWB4JVBQEAAHD+q3wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIDD6a3IWZ+jQe/0kbpjRIR3zIy1fYctPPzmH5igXhyKFsjYbMHj+9QuVNobZixHq+FCwkGL8dMIUlKc6rX+510A\"};\n"], "names": [], "mappings": ";;;;AAAA;;uCACe;IAAE,KAAA,mHAAG;IAAE,OAAO;IAAM,QAAQ;IAAM,WAAW;IAAG,YAAY;IAAG,aAAa;AAAyU", "debugId": null}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/about/about-hero.tsx"], "sourcesContent": ["import Image from 'next/image';\r\nimport { AspectRatio } from '@/components/ui/aspect-ratio';\r\nimport team from '@/public/team.webp';\r\n\r\nexport default function AboutHero() {\r\n  return (\r\n    <section className=\"my-12\">\r\n      <div className=\"mx-auto flex max-w-6xl flex-col items-start px-4 md:px-10 2xl:max-w-7xl\">\r\n        <p className=\"mb-8 font-aeonik-bold font-medium text-md text-muted-foreground/80 uppercase\">\r\n          OUR TEAM\r\n        </p>\r\n        <h2 className=\"mb-12 max-w-4xl font-medium font-sans text-2xl text-light leading-tight md:text-4xl lg:text-5xl lg:tracking-tight\">\r\n          We're a passionate group of engineers, designers, and researchers\r\n          building your favorite desktop AI assistant.\r\n        </h2>\r\n        <div className=\"mb-6 flex w-full flex-col justify-center\">\r\n          <AspectRatio className=\"w-full rounded-lg bg-muted\" ratio={16 / 9}>\r\n            <Image\r\n              alt=\"The Highlight AI team in New York\"\r\n              className=\"h-full w-full rounded-lg object-cover transition-all duration-700 ease-out\"\r\n              fill\r\n              src={team}\r\n            />\r\n          </AspectRatio>\r\n          <p className=\"mt-4 text-start font-medium text-md text-muted-foreground/80\">\r\n            On the steps where it all started. New York, 2024\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEe,SAAS;IACtB,qBACE,6WAAC;QAAQ,WAAU;kBACjB,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAE,WAAU;8BAA+E;;;;;;8BAG5F,6WAAC;oBAAG,WAAU;8BAAoH;;;;;;8BAIlI,6WAAC;oBAAI,WAAU;;sCACb,6WAAC,mJAAW;4BAAC,WAAU;4BAA6B,OAAO,KAAK;sCAC9D,cAAA,6WAAC,uQAAK;gCACJ,KAAI;gCACJ,WAAU;gCACV,IAAI;gCACJ,KAAK,0RAAI;;;;;;;;;;;sCAGb,6WAAC;4BAAE,WAAU;sCAA+D;;;;;;;;;;;;;;;;;;;;;;;AAOtF", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/about/about-more.tsx"], "sourcesContent": ["import Link from 'next/link';\r\n\r\nexport default function AboutMore() {\r\n  return (\r\n    <>\r\n      <section className=\"mx-auto mb-8 max-w-4xl px-4 py-8 font-aeonik md:px-10\">\r\n        <p className=\"mx-auto max-w-4xl font-aeonik font-medium text-light-80 text-xl leading-tight md:text-2xl lg:text-3xl\">\r\n          We're the same team behind{' '}\r\n          <a\r\n            className=\"text-brand-500 transition-colors hover:text-brand-500/80\"\r\n            href=\"https://rathon-rw.com\"\r\n            rel=\"noopener noreferrer\"\r\n            target=\"_blank\"\r\n          >\r\n            Rathon\r\n          </a>\r\n          , the largest Windows app for game recording, trusted by millions of\r\n          gamers worldwide.\r\n        </p>\r\n      </section>\r\n      <section className=\"mx-auto mb-[160px] max-w-4xl px-4 py-8 font-aeonik md:px-10\">\r\n        <p className=\"mx-auto max-w-4xl font-aeonik font-medium text-light-80 text-xl leading-tight md:text-2xl lg:text-3xl\">\r\n          We're always looking for amazing people to join us.{' '}\r\n          <Link\r\n            className=\"inline-flex items-center font-aeonik font-medium text-brand-500 text-xl transition-all duration-200 hover:text-brand-500/80 md:text-2xl lg:text-3xl\"\r\n            href=\"/jobs\"\r\n            rel=\"noopener noreferrer\"\r\n            target=\"_blank\"\r\n          >\r\n            See open positions →\r\n          </Link>\r\n        </p>\r\n      </section>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEe,SAAS;IACtB,qBACE;;0BACE,6WAAC;gBAAQ,WAAU;0BACjB,cAAA,6WAAC;oBAAE,WAAU;;wBAAwG;wBACxF;sCAC3B,6WAAC;4BACC,WAAU;4BACV,MAAK;4BACL,KAAI;4BACJ,QAAO;sCACR;;;;;;wBAEG;;;;;;;;;;;;0BAKR,6WAAC;gBAAQ,WAAU;0BACjB,cAAA,6WAAC;oBAAE,WAAU;;wBAAwG;wBAC/D;sCACpD,6WAAC,sSAAI;4BACH,WAAU;4BACV,MAAK;4BACL,KAAI;4BACJ,QAAO;sCACR;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 604, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/app/%28root%29/about/page.tsx"], "sourcesContent": ["import AboutHero from '@/features/about/about-hero';\r\nimport AboutMore from '@/features/about/about-more';\r\n\r\nexport default function AboutPage() {\r\n  return (\r\n    <main className=\"pt-[90px]\">\r\n      <AboutHero />\r\n      <AboutMore />\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,6WAAC;QAAK,WAAU;;0BACd,6WAAC,8IAAS;;;;;0BACV,6WAAC,8IAAS;;;;;;;;;;;AAGhB", "debugId": null}}]}