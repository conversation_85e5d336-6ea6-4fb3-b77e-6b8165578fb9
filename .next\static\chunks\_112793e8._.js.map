{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { Check, X } from 'lucide-react';\nimport { featuresCompare } from '@/config/docs';\nimport { cn } from '@/lib/utils';\n\nconst columns = ['Highlight', 'ChatGPT', 'Claude', 'Raycast', 'Notion'];\n\nexport default function FeatureTableLg() {\n  return (\n    <div className=\"no-scrollbar hidden w-full overflow-x-auto md:block\">\n      <table\n        className=\"w-full min-w-[700px] border-collapse border-spacing-0\"\n        style={{\n          borderSpacing: '0px',\n        }}\n      >\n        <thead>\n          <tr>\n            <th\n              className=\"sticky top-0 left-0 z-40 w-[200px] min-w-[200px] bg-[#090909] text-left font-normal text-sm md:w-[260px] md:min-w-[260px] md:text-base\"\n              scope=\"col\"\n            >\n              <div className=\"flex flex-row items-center gap-3 rounded-t-xl py-2\">\n                <span className=\"font-medium font-sans text-lg text-white\">\n                  Features\n                </span>\n              </div>\n            </th>\n            {columns.map((col, i) => (\n              <th\n                className=\"sticky top-0 left-[200px] z-30 w-[120px] min-w-[120px] bg-[#090909] px-0 text-center font-normal text-sm md:left-auto md:text-base\"\n                key={col}\n              >\n                <div\n                  className={cn(\n                    'flex flex-col items-center font-medium font-sans',\n                    i === 0\n                      ? 'rounded-t-xl py-2 text-primary'\n                      : 'p-4 text-muted-foreground'\n                  )}\n                >\n                  <div className=\"flex items-center gap-1.5\">\n                    <a className=\"flex items-center gap-1.5\" href=\"/\">\n                      <span className=\"pb-[1.5px] font-medium text-lg\">\n                        {col}\n                      </span>\n                    </a>\n                  </div>\n                </div>\n              </th>\n            ))}\n          </tr>\n        </thead>\n        <tbody>\n          {featuresCompare.map((row, i) => (\n            <tr className=\"border-0.5 border-muted/60 border-b\" key={i}>\n              <td className=\"sticky left-0 z-30 w-[200px] min-w-[200px] bg-[#090909] py-3 pr-2 align-middle md:w-[260px] md:min-w-[260px] md:pr-8\">\n                <span className=\"text-nowrap font-medium text-base text-muted-foreground leading-tight\">\n                  {row.feature}\n                </span>\n              </td>\n              {columns.map((col, i) => (\n                <td\n                  className={cn(\n                    'w-[120px] min-w-[120px] p-4 text-center align-middle',\n                    i === 0 && 'sticky left-[200px] z-20 bg-[#090909] md:static'\n                  )}\n                  key={col}\n                >\n                  <div\n                    className={cn(\n                      'mx-auto flex size-6 items-center justify-center rounded-lg text-black md:size-7',\n                      row[col as keyof typeof row]\n                        ? 'bg-brand-400'\n                        : 'bg-brand-100'\n                    )}\n                  >\n                    {row[col as keyof typeof row] ? (\n                      <Check className=\"mx-auto size-4 text-black\" />\n                    ) : (\n                      <X className=\"mx-auto size-4 text-brand-600\" />\n                    )}\n                  </div>\n                </td>\n              ))}\n            </tr>\n          ))}\n        </tbody>\n      </table>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AACA;AACA;AAJA;;;;;AAMA,MAAM,UAAU;IAAC;IAAa;IAAW;IAAU;IAAW;CAAS;AAExD,SAAS;IACtB,qBACE,4TAAC;QAAI,WAAU;kBACb,cAAA,4TAAC;YACC,WAAU;YACV,OAAO;gBACL,eAAe;YACjB;;8BAEA,4TAAC;8BACC,cAAA,4TAAC;;0CACC,4TAAC;gCACC,WAAU;gCACV,OAAM;0CAEN,cAAA,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;;;;;4BAK9D,QAAQ,GAAG,CAAC,CAAC,KAAK,kBACjB,4TAAC;oCACC,WAAU;8CAGV,cAAA,4TAAC;wCACC,WAAW,IAAA,qHAAE,EACX,oDACA,MAAM,IACF,mCACA;kDAGN,cAAA,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC;gDAAE,WAAU;gDAA4B,MAAK;0DAC5C,cAAA,4TAAC;oDAAK,WAAU;8DACb;;;;;;;;;;;;;;;;;;;;;mCAbJ;;;;;;;;;;;;;;;;8BAsBb,4TAAC;8BACE,oIAAe,CAAC,GAAG,CAAC,CAAC,KAAK,kBACzB,4TAAC;4BAAG,WAAU;;8CACZ,4TAAC;oCAAG,WAAU;8CACZ,cAAA,4TAAC;wCAAK,WAAU;kDACb,IAAI,OAAO;;;;;;;;;;;gCAGf,QAAQ,GAAG,CAAC,CAAC,KAAK,kBACjB,4TAAC;wCACC,WAAW,IAAA,qHAAE,EACX,wDACA,MAAM,KAAK;kDAIb,cAAA,4TAAC;4CACC,WAAW,IAAA,qHAAE,EACX,mFACA,GAAG,CAAC,IAAwB,GACxB,iBACA;sDAGL,GAAG,CAAC,IAAwB,iBAC3B,4TAAC,oSAAK;gDAAC,WAAU;;;;;qEAEjB,4TAAC,wRAAC;gDAAC,WAAU;;;;;;;;;;;uCAbZ;;;;;;2BAZ8C;;;;;;;;;;;;;;;;;;;;;AAoCrE;KApFwB", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["/** biome-ignore-all lint/suspicious/noArrayIndexKey: <explanation> */\n'use client';\n\nimport { Check, X } from 'lucide-react';\nimport { featuresCompare } from '@/config/docs';\nimport { cn } from '@/lib/utils';\n\nconst tools = ['Highlight', 'ChatGPT', 'Claude', 'Raycast', 'Notion'];\n\nexport default function FeatureTableSm() {\n  return (\n    <div className=\"no-scrollbar block w-full overflow-x-auto md:hidden\">\n      <table\n        className=\"w-max min-w-[700px] border-collapse border-spacing-0\"\n        style={{\n          borderSpacing: '0px',\n        }}\n      >\n        <thead>\n          <tr>\n            <th className=\"sticky top-0 left-0 z-50 w-[120px] min-w-[120px] bg-[#090909] text-left\" />\n\n            {featuresCompare.map((f, i) => (\n              <th\n                className=\"sticky top-0 z-40 w-[120px] min-w-[120px] bg-[#090909] text-center font-normal text-muted-foreground text-sm\"\n                key={i}\n              >\n                <span className=\"block px-3 py-2 text-base leading-tight\">\n                  {f.feature}\n                </span>\n              </th>\n            ))}\n          </tr>\n        </thead>\n        <tbody>\n          {tools.map((tool, i) => (\n            <tr className=\"border-0.5 border-muted/60 border-b\" key={i}>\n              <td className=\"sticky left-0 z-40 w-[120px] min-w-[120px] bg-[#090909] px-3 py-3 pr-4 align-middle\">\n                <span className=\"text-nowrap font-medium font-sans text-base text-primary leading-tight\">\n                  {tool}\n                </span>\n              </td>\n              {featuresCompare.map((f, index) => (\n                <td\n                  className={cn(\n                    'w-[120px] min-w-[120px] p-4 text-center align-middle'\n                  )}\n                  key={index}\n                >\n                  <div\n                    className={cn(\n                      'mx-auto flex size-6 items-center justify-center rounded-lg text-black md:size-7',\n                      f[tool as keyof typeof f]\n                        ? 'bg-brand-400'\n                        : 'bg-brand-100'\n                    )}\n                  >\n                    {f[tool as keyof typeof f] ? (\n                      <Check className=\"mx-auto size-4 text-black\" />\n                    ) : (\n                      <X className=\"mx-auto size-4 text-brand-600\" />\n                    )}\n                  </div>\n                </td>\n              ))}\n            </tr>\n          ))}\n        </tbody>\n      </table>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,oEAAoE;;;;;AAGpE;AAAA;AACA;AACA;AAJA;;;;;AAMA,MAAM,QAAQ;IAAC;IAAa;IAAW;IAAU;IAAW;CAAS;AAEtD,SAAS;IACtB,qBACE,4TAAC;QAAI,WAAU;kBACb,cAAA,4TAAC;YACC,WAAU;YACV,OAAO;gBACL,eAAe;YACjB;;8BAEA,4TAAC;8BACC,cAAA,4TAAC;;0CACC,4TAAC;gCAAG,WAAU;;;;;;4BAEb,oIAAe,CAAC,GAAG,CAAC,CAAC,GAAG,kBACvB,4TAAC;oCACC,WAAU;8CAGV,cAAA,4TAAC;wCAAK,WAAU;kDACb,EAAE,OAAO;;;;;;mCAHP;;;;;;;;;;;;;;;;8BASb,4TAAC;8BACE,MAAM,GAAG,CAAC,CAAC,MAAM,kBAChB,4TAAC;4BAAG,WAAU;;8CACZ,4TAAC;oCAAG,WAAU;8CACZ,cAAA,4TAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;gCAGJ,oIAAe,CAAC,GAAG,CAAC,CAAC,GAAG,sBACvB,4TAAC;wCACC,WAAW,IAAA,qHAAE,EACX;kDAIF,cAAA,4TAAC;4CACC,WAAW,IAAA,qHAAE,EACX,mFACA,CAAC,CAAC,KAAuB,GACrB,iBACA;sDAGL,CAAC,CAAC,KAAuB,iBACxB,4TAAC,oSAAK;gDAAC,WAAU;;;;;qEAEjB,4TAAC,wRAAC;gDAAC,WAAU;;;;;;;;;;;uCAbZ;;;;;;2BAX8C;;;;;;;;;;;;;;;;;;;;;AAmCrE;KA9DwB", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/kibo-ui/image-zoom/index.tsx"], "sourcesContent": ["'use client';\n\nimport Zoom, {\n  type ControlledProps,\n  type UncontrolledProps,\n} from 'react-medium-image-zoom';\nimport { cn } from '@/lib/utils';\n\nexport type ImageZoomProps = UncontrolledProps & {\n  isZoomed?: ControlledProps['isZoomed'];\n  onZoomChange?: ControlledProps['onZoomChange'];\n  className?: string;\n  backdropClassName?: string;\n};\n\nexport const ImageZoom = ({\n  className,\n  backdropClassName,\n  ...props\n}: ImageZoomProps) => (\n  <div\n    className={cn(\n      'relative',\n      '[&_[data-rmiz-ghost]]:pointer-events-none [&_[data-rmiz-ghost]]:absolute',\n      '[&_[data-rmiz-btn-zoom]]:m-0 [&_[data-rmiz-btn-zoom]]:size-10 [&_[data-rmiz-btn-zoom]]:touch-manipulation [&_[data-rmiz-btn-zoom]]:appearance-none [&_[data-rmiz-btn-zoom]]:rounded-[50%] [&_[data-rmiz-btn-zoom]]:border-none [&_[data-rmiz-btn-zoom]]:bg-foreground/70 [&_[data-rmiz-btn-zoom]]:p-2 [&_[data-rmiz-btn-zoom]]:text-background [&_[data-rmiz-btn-zoom]]:outline-offset-2',\n      '[&_[data-rmiz-btn-unzoom]]:m-0 [&_[data-rmiz-btn-unzoom]]:size-10 [&_[data-rmiz-btn-unzoom]]:touch-manipulation [&_[data-rmiz-btn-unzoom]]:appearance-none [&_[data-rmiz-btn-unzoom]]:rounded-[50%] [&_[data-rmiz-btn-unzoom]]:border-none [&_[data-rmiz-btn-unzoom]]:bg-foreground/70 [&_[data-rmiz-btn-unzoom]]:p-2 [&_[data-rmiz-btn-unzoom]]:text-background [&_[data-rmiz-btn-unzoom]]:outline-offset-2',\n      '[&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:pointer-events-none [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:absolute [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:size-px [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:overflow-hidden [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:whitespace-nowrap [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:[clip-path:inset(50%)] [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:[clip:rect(0_0_0_0)]',\n      '[&_[data-rmiz-btn-zoom]]:absolute [&_[data-rmiz-btn-zoom]]:top-2.5 [&_[data-rmiz-btn-zoom]]:right-2.5 [&_[data-rmiz-btn-zoom]]:bottom-auto [&_[data-rmiz-btn-zoom]]:left-auto [&_[data-rmiz-btn-zoom]]:cursor-zoom-in',\n      '[&_[data-rmiz-btn-unzoom]]:absolute [&_[data-rmiz-btn-unzoom]]:top-5 [&_[data-rmiz-btn-unzoom]]:right-5 [&_[data-rmiz-btn-unzoom]]:bottom-auto [&_[data-rmiz-btn-unzoom]]:left-auto [&_[data-rmiz-btn-unzoom]]:z-[1] [&_[data-rmiz-btn-unzoom]]:cursor-zoom-out',\n      '[&_[data-rmiz-content=\"found\"]_img]:cursor-zoom-in',\n      '[&_[data-rmiz-content=\"found\"]_svg]:cursor-zoom-in',\n      '[&_[data-rmiz-content=\"found\"]_[role=\"img\"]]:cursor-zoom-in',\n      '[&_[data-rmiz-content=\"found\"]_[data-zoom]]:cursor-zoom-in',\n      className\n    )}\n  >\n    <Zoom\n      classDialog={cn(\n        '[&::backdrop]:hidden',\n        '[&[open]]:fixed [&[open]]:m-0 [&[open]]:h-dvh [&[open]]:max-h-none [&[open]]:w-dvw [&[open]]:max-w-none [&[open]]:overflow-hidden [&[open]]:border-0 [&[open]]:bg-transparent [&[open]]:p-0',\n        '[&_[data-rmiz-modal-overlay]]:absolute [&_[data-rmiz-modal-overlay]]:inset-0 [&_[data-rmiz-modal-overlay]]:transition-all',\n        '[&_[data-rmiz-modal-overlay=\"hidden\"]]:bg-transparent',\n        '[&_[data-rmiz-modal-overlay=\"visible\"]]:bg-background/80 [&_[data-rmiz-modal-overlay=\"visible\"]]:backdrop-blur-md',\n        '[&_[data-rmiz-modal-content]]:relative [&_[data-rmiz-modal-content]]:size-full',\n        '[&_[data-rmiz-modal-img]]:absolute [&_[data-rmiz-modal-img]]:origin-top-left [&_[data-rmiz-modal-img]]:cursor-zoom-out [&_[data-rmiz-modal-img]]:transition-transform',\n        'motion-reduce:[&_[data-rmiz-modal-img]]:transition-none motion-reduce:[&_[data-rmiz-modal-overlay]]:transition-none',\n        backdropClassName\n      )}\n      {...props}\n    />\n  </div>\n);\n"], "names": [], "mappings": ";;;;;AAEA;AAIA;AANA;;;;AAeO,MAAM,YAAY;QAAC,EACxB,SAAS,EACT,iBAAiB,EACjB,GAAG,OACY;yBACf,4TAAC;QACC,WAAW,IAAA,qHAAE,EACX,YACA,4EACA,4XACA,gZACA,odACA,yNACA,mQACA,sDACA,sDACA,+DACA,8DACA;kBAGF,cAAA,4TAAC,wRAAI;YACH,aAAa,IAAA,qHAAE,EACb,wBACA,+LACA,6HACA,yDACA,qHACA,kFACA,yKACA,uHACA;YAED,GAAG,KAAK;;;;;;;;;;;;KAjCF", "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/accordion.tsx"], "sourcesContent": ["'use client';\n\nimport * as AccordionPrimitive from '@radix-ui/react-accordion';\nimport { ChevronDownIcon } from 'lucide-react';\nimport type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Accordion({\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Root>) {\n  return <AccordionPrimitive.Root data-slot=\"accordion\" {...props} />;\n}\n\nfunction AccordionItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Item>) {\n  return (\n    <AccordionPrimitive.Item\n      className={cn('border-b last:border-b-0', className)}\n      data-slot=\"accordion-item\"\n      {...props}\n    />\n  );\n}\n\nfunction AccordionTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {\n  return (\n    <AccordionPrimitive.Header className=\"flex\">\n      <AccordionPrimitive.Trigger\n        className={cn(\n          'flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left font-medium text-sm outline-none transition-all hover:underline focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180',\n          className\n        )}\n        data-slot=\"accordion-trigger\"\n        {...props}\n      >\n        {children}\n        <ChevronDownIcon className=\"pointer-events-none size-4 shrink-0 translate-y-0.5 text-muted-foreground transition-transform duration-200\" />\n      </AccordionPrimitive.Trigger>\n    </AccordionPrimitive.Header>\n  );\n}\n\nfunction AccordionContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Content>) {\n  return (\n    <AccordionPrimitive.Content\n      className=\"overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\n      data-slot=\"accordion-content\"\n      {...props}\n    >\n      <div className={cn('pt-0 pb-4', className)}>{children}</div>\n    </AccordionPrimitive.Content>\n  );\n}\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent };\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AAGA;AANA;;;;;AAQA,SAAS,UAAU,KAEoC;QAFpC,EACjB,GAAG,OACkD,GAFpC;IAGjB,qBAAO,4TAAC,uRAAuB;QAAC,aAAU;QAAa,GAAG,KAAK;;;;;;AACjE;KAJS;AAMT,SAAS,cAAc,KAGgC;QAHhC,EACrB,SAAS,EACT,GAAG,OACkD,GAHhC;IAIrB,qBACE,4TAAC,uRAAuB;QACtB,WAAW,IAAA,qHAAE,EAAC,4BAA4B;QAC1C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,KAIgC;QAJhC,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD,GAJhC;IAKxB,qBACE,4TAAC,yRAAyB;QAAC,WAAU;kBACnC,cAAA,4TAAC,0RAA0B;YACzB,WAAW,IAAA,qHAAE,EACX,8SACA;YAEF,aAAU;YACT,GAAG,KAAK;;gBAER;8BACD,4TAAC,kUAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MApBS;AAsBT,SAAS,iBAAiB,KAIgC;QAJhC,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD,GAJhC;IAKxB,qBACE,4TAAC,0RAA0B;QACzB,WAAU;QACV,aAAU;QACT,GAAG,KAAK;kBAET,cAAA,4TAAC;YAAI,WAAW,IAAA,qHAAE,EAAC,aAAa;sBAAa;;;;;;;;;;;AAGnD;MAdS", "debugId": null}}, {"offset": {"line": 479, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/infinite-slider.tsx"], "sourcesContent": ["'use client';\nimport { animate, motion, useMotionValue } from 'motion/react';\nimport { useEffect, useState } from 'react';\nimport useMeasure from 'react-use-measure';\nimport { cn } from '@/lib/utils';\n\nexport type InfiniteSliderProps = {\n  children: React.ReactNode;\n  gap?: number;\n  speed?: number;\n  speedOnHover?: number;\n  direction?: 'horizontal' | 'vertical';\n  reverse?: boolean;\n  className?: string;\n};\n\nexport function InfiniteSlider({\n  children,\n  gap = 16,\n  speed = 100,\n  speedOnHover,\n  direction = 'horizontal',\n  reverse = false,\n  className,\n}: InfiniteSliderProps) {\n  const [currentSpeed, setCurrentSpeed] = useState(speed);\n  const [ref, { width, height }] = useMeasure();\n  const translation = useMotionValue(0);\n  const [isTransitioning, setIsTransitioning] = useState(false);\n  const [key, setKey] = useState(0);\n\n  useEffect(() => {\n    let controls;\n    const size = direction === 'horizontal' ? width : height;\n    const contentSize = size + gap;\n    const from = reverse ? -contentSize / 2 : 0;\n    const to = reverse ? 0 : -contentSize / 2;\n\n    const distanceToTravel = Math.abs(to - from);\n    const duration = distanceToTravel / currentSpeed;\n\n    if (isTransitioning) {\n      const remainingDistance = Math.abs(translation.get() - to);\n      const transitionDuration = remainingDistance / currentSpeed;\n\n      controls = animate(translation, [translation.get(), to], {\n        ease: 'linear',\n        duration: transitionDuration,\n        onComplete: () => {\n          setIsTransitioning(false);\n          setKey((prevKey) => prevKey + 1);\n        },\n      });\n    } else {\n      controls = animate(translation, [from, to], {\n        ease: 'linear',\n        duration,\n        repeat: Number.POSITIVE_INFINITY,\n        repeatType: 'loop',\n        repeatDelay: 0,\n        onRepeat: () => {\n          translation.set(from);\n        },\n      });\n    }\n\n    return controls?.stop;\n  }, [\n    key,\n    translation,\n    currentSpeed,\n    width,\n    height,\n    gap,\n    isTransitioning,\n    direction,\n    reverse,\n  ]);\n\n  const hoverProps = speedOnHover\n    ? {\n        onHoverStart: () => {\n          setIsTransitioning(true);\n          setCurrentSpeed(speedOnHover);\n        },\n        onHoverEnd: () => {\n          setIsTransitioning(true);\n          setCurrentSpeed(speed);\n        },\n      }\n    : {};\n\n  return (\n    <div className={cn('overflow-hidden', className)}>\n      <motion.div\n        className=\"flex w-max\"\n        ref={ref}\n        style={{\n          ...(direction === 'horizontal'\n            ? { x: translation }\n            : { y: translation }),\n          gap: `${gap}px`,\n          flexDirection: direction === 'horizontal' ? 'row' : 'column',\n        }}\n        {...hoverProps}\n      >\n        {children}\n        {children}\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AAAA;AACA;AACA;AACA;;;AAJA;;;;;AAgBO,SAAS,eAAe,KAQT;QARS,EAC7B,QAAQ,EACR,MAAM,EAAE,EACR,QAAQ,GAAG,EACX,YAAY,EACZ,YAAY,YAAY,EACxB,UAAU,KAAK,EACf,SAAS,EACW,GARS;;IAS7B,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,wSAAQ,EAAC;IACjD,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,GAAG,IAAA,+QAAU;IAC3C,MAAM,cAAc,IAAA,6SAAc,EAAC;IACnC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,wSAAQ,EAAC;IACvD,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,wSAAQ,EAAC;IAE/B,IAAA,ySAAS;oCAAC;YACR,IAAI;YACJ,MAAM,OAAO,cAAc,eAAe,QAAQ;YAClD,MAAM,cAAc,OAAO;YAC3B,MAAM,OAAO,UAAU,CAAC,cAAc,IAAI;YAC1C,MAAM,KAAK,UAAU,IAAI,CAAC,cAAc;YAExC,MAAM,mBAAmB,KAAK,GAAG,CAAC,KAAK;YACvC,MAAM,WAAW,mBAAmB;YAEpC,IAAI,iBAAiB;gBACnB,MAAM,oBAAoB,KAAK,GAAG,CAAC,YAAY,GAAG,KAAK;gBACvD,MAAM,qBAAqB,oBAAoB;gBAE/C,WAAW,IAAA,oSAAO,EAAC,aAAa;oBAAC,YAAY,GAAG;oBAAI;iBAAG,EAAE;oBACvD,MAAM;oBACN,UAAU;oBACV,UAAU;oDAAE;4BACV,mBAAmB;4BACnB;4DAAO,CAAC,UAAY,UAAU;;wBAChC;;gBACF;YACF,OAAO;gBACL,WAAW,IAAA,oSAAO,EAAC,aAAa;oBAAC;oBAAM;iBAAG,EAAE;oBAC1C,MAAM;oBACN;oBACA,QAAQ,OAAO,iBAAiB;oBAChC,YAAY;oBACZ,aAAa;oBACb,QAAQ;oDAAE;4BACR,YAAY,GAAG,CAAC;wBAClB;;gBACF;YACF;YAEA,OAAO,qBAAA,+BAAA,SAAU,IAAI;QACvB;mCAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,aAAa,eACf;QACE,cAAc;YACZ,mBAAmB;YACnB,gBAAgB;QAClB;QACA,YAAY;YACV,mBAAmB;YACnB,gBAAgB;QAClB;IACF,IACA,CAAC;IAEL,qBACE,4TAAC;QAAI,WAAW,IAAA,qHAAE,EAAC,mBAAmB;kBACpC,cAAA,4TAAC,6SAAM,CAAC,GAAG;YACT,WAAU;YACV,KAAK;YACL,OAAO;gBACL,GAAI,cAAc,eACd;oBAAE,GAAG;gBAAY,IACjB;oBAAE,GAAG;gBAAY,CAAC;gBACtB,KAAK,AAAC,GAAM,OAAJ,KAAI;gBACZ,eAAe,cAAc,eAAe,QAAQ;YACtD;YACC,GAAG,UAAU;;gBAEb;gBACA;;;;;;;;;;;;AAIT;GA/FgB;;QAUmB,+QAAU;QACvB,6SAAc;;;KAXpB", "debugId": null}}, {"offset": {"line": 618, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/progressive-blur.tsx"], "sourcesContent": ["'use client';\nimport { type HTMLMotionProps, motion } from 'motion/react';\nimport { cn } from '@/lib/utils';\n\nexport const GRADIENT_ANGLES = {\n  top: 0,\n  right: 90,\n  bottom: 180,\n  left: 270,\n};\n\nexport type ProgressiveBlurProps = {\n  direction?: keyof typeof GRADIENT_ANGLES;\n  blurLayers?: number;\n  className?: string;\n  blurIntensity?: number;\n} & HTMLMotionProps<'div'>;\n\nexport function ProgressiveBlur({\n  direction = 'bottom',\n  blurLayers = 8,\n  className,\n  blurIntensity = 0.25,\n  ...props\n}: ProgressiveBlurProps) {\n  const layers = Math.max(blurLayers, 2);\n  const segmentSize = 1 / (blurLayers + 1);\n\n  return (\n    <div className={cn('relative', className)}>\n      {Array.from({ length: layers }).map((_, index) => {\n        const angle = GRADIENT_ANGLES[direction];\n        const gradientStops = [\n          index * segmentSize,\n          (index + 1) * segmentSize,\n          (index + 2) * segmentSize,\n          (index + 3) * segmentSize,\n        ].map(\n          (pos, posIndex) =>\n            `rgba(255, 255, 255, ${posIndex === 1 || posIndex === 2 ? 1 : 0}) ${pos * 100}%`\n        );\n\n        const gradient = `linear-gradient(${angle}deg, ${gradientStops.join(\n          ', '\n        )})`;\n\n        return (\n          <motion.div\n            className=\"pointer-events-none absolute inset-0 rounded-[inherit]\"\n            key={index}\n            style={{\n              maskImage: gradient,\n              WebkitMaskImage: gradient,\n              backdropFilter: `blur(${index * blurIntensity}px)`,\n              WebkitBackdropFilter: `blur(${index * blurIntensity}px)`,\n            }}\n            {...props}\n          />\n        );\n      })}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAFA;;;;AAIO,MAAM,kBAAkB;IAC7B,KAAK;IACL,OAAO;IACP,QAAQ;IACR,MAAM;AACR;AASO,SAAS,gBAAgB,KAMT;QANS,EAC9B,YAAY,QAAQ,EACpB,aAAa,CAAC,EACd,SAAS,EACT,gBAAgB,IAAI,EACpB,GAAG,OACkB,GANS;IAO9B,MAAM,SAAS,KAAK,GAAG,CAAC,YAAY;IACpC,MAAM,cAAc,IAAI,CAAC,aAAa,CAAC;IAEvC,qBACE,4TAAC;QAAI,WAAW,IAAA,qHAAE,EAAC,YAAY;kBAC5B,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAO,GAAG,GAAG,CAAC,CAAC,GAAG;YACtC,MAAM,QAAQ,eAAe,CAAC,UAAU;YACxC,MAAM,gBAAgB;gBACpB,QAAQ;gBACR,CAAC,QAAQ,CAAC,IAAI;gBACd,CAAC,QAAQ,CAAC,IAAI;gBACd,CAAC,QAAQ,CAAC,IAAI;aACf,CAAC,GAAG,CACH,CAAC,KAAK,WACJ,AAAC,uBAAmE,OAA7C,aAAa,KAAK,aAAa,IAAI,IAAI,GAAE,MAAc,OAAV,MAAM,KAAI;YAGlF,MAAM,WAAW,AAAC,mBAA+B,OAAb,OAAM,SAExC,OAF+C,cAAc,IAAI,CACjE,OACA;YAEF,qBACE,4TAAC,6SAAM,CAAC,GAAG;gBACT,WAAU;gBAEV,OAAO;oBACL,WAAW;oBACX,iBAAiB;oBACjB,gBAAgB,AAAC,QAA6B,OAAtB,QAAQ,eAAc;oBAC9C,sBAAsB,AAAC,QAA6B,OAAtB,QAAQ,eAAc;gBACtD;gBACC,GAAG,KAAK;eAPJ;;;;;QAUX;;;;;;AAGN;KA5CgB", "debugId": null}}, {"offset": {"line": 685, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/carousel.tsx"], "sourcesContent": ["'use client';\n\nimport useEmblaCarousel, {\n  type UseEmblaCarouselType,\n} from 'embla-carousel-react';\nimport { ArrowLeft, ArrowRight } from 'lucide-react';\nimport React from 'react';\nimport { Button } from '@/components/ui/button';\nimport { cn } from '@/lib/utils';\n\ntype CarouselApi = UseEmblaCarouselType[1];\ntype UseCarouselParameters = Parameters<typeof useEmblaCarousel>;\ntype CarouselOptions = UseCarouselParameters[0];\ntype CarouselPlugin = UseCarouselParameters[1];\n\ntype CarouselProps = {\n  opts?: CarouselOptions;\n  plugins?: CarouselPlugin;\n  orientation?: 'horizontal' | 'vertical';\n  setApi?: (api: CarouselApi) => void;\n};\n\ntype CarouselContextProps = {\n  carouselRef: ReturnType<typeof useEmblaCarousel>[0];\n  api: ReturnType<typeof useEmblaCarousel>[1];\n  scrollPrev: () => void;\n  scrollNext: () => void;\n  canScrollPrev: boolean;\n  canScrollNext: boolean;\n} & CarouselProps;\n\nconst CarouselContext = React.createContext<CarouselContextProps | null>(null);\n\nexport function useCarousel() {\n  const context = React.useContext(CarouselContext);\n\n  if (!context) {\n    throw new Error('useCarousel must be used within a <Carousel />');\n  }\n\n  return context;\n}\n\nfunction Carousel({\n  orientation = 'horizontal',\n  opts,\n  setApi,\n  plugins,\n  className,\n  children,\n  ...props\n}: React.ComponentProps<'div'> & CarouselProps) {\n  const [carouselRef, api] = useEmblaCarousel(\n    {\n      ...opts,\n      axis: orientation === 'horizontal' ? 'x' : 'y',\n    },\n    plugins\n  );\n  const [canScrollPrev, setCanScrollPrev] = React.useState(false);\n  const [canScrollNext, setCanScrollNext] = React.useState(false);\n\n  const onSelect = React.useCallback((api: CarouselApi) => {\n    if (!api) return;\n    setCanScrollPrev(api.canScrollPrev());\n    setCanScrollNext(api.canScrollNext());\n  }, []);\n\n  const scrollPrev = React.useCallback(() => {\n    api?.scrollPrev();\n  }, [api]);\n\n  const scrollNext = React.useCallback(() => {\n    api?.scrollNext();\n  }, [api]);\n\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent<HTMLDivElement>) => {\n      if (event.key === 'ArrowLeft') {\n        event.preventDefault();\n        scrollPrev();\n      } else if (event.key === 'ArrowRight') {\n        event.preventDefault();\n        scrollNext();\n      }\n    },\n    [scrollPrev, scrollNext]\n  );\n\n  React.useEffect(() => {\n    if (!(api && setApi)) return;\n    setApi(api);\n  }, [api, setApi]);\n\n  React.useEffect(() => {\n    if (!api) return;\n    onSelect(api);\n    api.on('reInit', onSelect);\n    api.on('select', onSelect);\n\n    return () => {\n      api?.off('select', onSelect);\n    };\n  }, [api, onSelect]);\n\n  return (\n    <CarouselContext.Provider\n      value={{\n        carouselRef,\n        api,\n        opts,\n        orientation:\n          orientation || (opts?.axis === 'y' ? 'vertical' : 'horizontal'),\n        scrollPrev,\n        scrollNext,\n        canScrollPrev,\n        canScrollNext,\n      }}\n    >\n      <div\n        aria-roledescription=\"carousel\"\n        className={cn('relative', className)}\n        data-slot=\"carousel\"\n        onKeyDownCapture={handleKeyDown}\n        role=\"region\"\n        {...props}\n      >\n        {children}\n      </div>\n    </CarouselContext.Provider>\n  );\n}\n\nfunction CarouselContent({ className, ...props }: React.ComponentProps<'div'>) {\n  const { carouselRef, orientation } = useCarousel();\n\n  return (\n    <div\n      className=\"overflow-hidden\"\n      data-slot=\"carousel-content\"\n      ref={carouselRef}\n    >\n      <div\n        className={cn(\n          'flex',\n          orientation === 'horizontal' ? '-ml-4' : '-mt-4 flex-col',\n          className\n        )}\n        {...props}\n      />\n    </div>\n  );\n}\n\nfunction CarouselItem({ className, ...props }: React.ComponentProps<'div'>) {\n  const { orientation } = useCarousel();\n\n  return (\n    <div\n      aria-roledescription=\"slide\"\n      className={cn(\n        'min-w-0 shrink-0 grow-0 basis-full',\n        orientation === 'horizontal' ? 'pl-4' : 'pt-4',\n        className\n      )}\n      data-slot=\"carousel-item\"\n      role=\"group\"\n      {...props}\n    />\n  );\n}\n\nfunction CarouselPrevious({\n  className,\n  variant = 'outline',\n  size = 'icon',\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const { orientation, scrollPrev, canScrollPrev } = useCarousel();\n\n  return (\n    <Button\n      className={cn(\n        'absolute size-8 rounded-full',\n        orientation === 'horizontal'\n          ? '-left-12 -translate-y-1/2 top-1/2'\n          : '-top-12 -translate-x-1/2 left-1/2 rotate-90',\n        className\n      )}\n      data-slot=\"carousel-previous\"\n      disabled={!canScrollPrev}\n      onClick={scrollPrev}\n      size={size}\n      variant={variant}\n      {...props}\n    >\n      <ArrowLeft />\n      <span className=\"sr-only\">Previous slide</span>\n    </Button>\n  );\n}\n\nfunction CarouselNext({\n  className,\n  variant = 'outline',\n  size = 'icon',\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const { orientation, scrollNext, canScrollNext } = useCarousel();\n\n  return (\n    <Button\n      className={cn(\n        'absolute size-8 rounded-full',\n        orientation === 'horizontal'\n          ? '-right-12 -translate-y-1/2 top-1/2'\n          : '-bottom-12 -translate-x-1/2 left-1/2 rotate-90',\n        className\n      )}\n      data-slot=\"carousel-next\"\n      disabled={!canScrollNext}\n      onClick={scrollNext}\n      size={size}\n      variant={variant}\n      {...props}\n    >\n      <ArrowRight />\n      <span className=\"sr-only\">Next slide</span>\n    </Button>\n  );\n}\n\nexport {\n  type CarouselApi,\n  Carousel,\n  CarouselContent,\n  CarouselItem,\n  CarouselPrevious,\n  CarouselNext,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAEA;AAGA;AAAA;AACA;AACA;AACA;;;AARA;;;;;;AA+BA,MAAM,gCAAkB,uSAAK,CAAC,aAAa,CAA8B;AAElE,SAAS;;IACd,MAAM,UAAU,uSAAK,CAAC,UAAU,CAAC;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GARgB;AAUhB,SAAS,SAAS,KAQ4B;QAR5B,EAChB,cAAc,YAAY,EAC1B,IAAI,EACJ,MAAM,EACN,OAAO,EACP,SAAS,EACT,QAAQ,EACR,GAAG,OACyC,GAR5B;;IAShB,MAAM,CAAC,aAAa,IAAI,GAAG,IAAA,iSAAgB,EACzC;QACE,GAAG,IAAI;QACP,MAAM,gBAAgB,eAAe,MAAM;IAC7C,GACA;IAEF,MAAM,CAAC,eAAe,iBAAiB,GAAG,uSAAK,CAAC,QAAQ,CAAC;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,uSAAK,CAAC,QAAQ,CAAC;IAEzD,MAAM,WAAW,uSAAK,CAAC,WAAW;0CAAC,CAAC;YAClC,IAAI,CAAC,KAAK;YACV,iBAAiB,IAAI,aAAa;YAClC,iBAAiB,IAAI,aAAa;QACpC;yCAAG,EAAE;IAEL,MAAM,aAAa,uSAAK,CAAC,WAAW;4CAAC;YACnC,gBAAA,0BAAA,IAAK,UAAU;QACjB;2CAAG;QAAC;KAAI;IAER,MAAM,aAAa,uSAAK,CAAC,WAAW;4CAAC;YACnC,gBAAA,0BAAA,IAAK,UAAU;QACjB;2CAAG;QAAC;KAAI;IAER,MAAM,gBAAgB,uSAAK,CAAC,WAAW;+CACrC,CAAC;YACC,IAAI,MAAM,GAAG,KAAK,aAAa;gBAC7B,MAAM,cAAc;gBACpB;YACF,OAAO,IAAI,MAAM,GAAG,KAAK,cAAc;gBACrC,MAAM,cAAc;gBACpB;YACF;QACF;8CACA;QAAC;QAAY;KAAW;IAG1B,uSAAK,CAAC,SAAS;8BAAC;YACd,IAAI,CAAC,CAAC,OAAO,MAAM,GAAG;YACtB,OAAO;QACT;6BAAG;QAAC;QAAK;KAAO;IAEhB,uSAAK,CAAC,SAAS;8BAAC;YACd,IAAI,CAAC,KAAK;YACV,SAAS;YACT,IAAI,EAAE,CAAC,UAAU;YACjB,IAAI,EAAE,CAAC,UAAU;YAEjB;sCAAO;oBACL,gBAAA,0BAAA,IAAK,GAAG,CAAC,UAAU;gBACrB;;QACF;6BAAG;QAAC;QAAK;KAAS;IAElB,qBACE,4TAAC,gBAAgB,QAAQ;QACvB,OAAO;YACL;YACA;YACA;YACA,aACE,eAAe,CAAC,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,MAAM,aAAa,YAAY;YAChE;YACA;YACA;YACA;QACF;kBAEA,cAAA,4TAAC;YACC,wBAAqB;YACrB,WAAW,IAAA,qHAAE,EAAC,YAAY;YAC1B,aAAU;YACV,kBAAkB;YAClB,MAAK;YACJ,GAAG,KAAK;sBAER;;;;;;;;;;;AAIT;IAxFS;;QASoB,iSAAgB;;;KATpC;AA0FT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;;IACvB,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;IAErC,qBACE,4TAAC;QACC,WAAU;QACV,aAAU;QACV,KAAK;kBAEL,cAAA,4TAAC;YACC,WAAW,IAAA,qHAAE,EACX,QACA,gBAAgB,eAAe,UAAU,kBACzC;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;IAnBS;;QAC8B;;;MAD9B;AAqBT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;;IACpB,MAAM,EAAE,WAAW,EAAE,GAAG;IAExB,qBACE,4TAAC;QACC,wBAAqB;QACrB,WAAW,IAAA,qHAAE,EACX,sCACA,gBAAgB,eAAe,SAAS,QACxC;QAEF,aAAU;QACV,MAAK;QACJ,GAAG,KAAK;;;;;;AAGf;IAhBS;;QACiB;;;MADjB;AAkBT,SAAS,iBAAiB,KAKY;QALZ,EACxB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,MAAM,EACb,GAAG,OACiC,GALZ;;IAMxB,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACE,4TAAC,wIAAM;QACL,WAAW,IAAA,qHAAE,EACX,gCACA,gBAAgB,eACZ,sCACA,+CACJ;QAEF,aAAU;QACV,UAAU,CAAC;QACX,SAAS;QACT,MAAM;QACN,SAAS;QACR,GAAG,KAAK;;0BAET,4TAAC,oTAAS;;;;;0BACV,4TAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;IA5BS;;QAM4C;;;MAN5C;AA8BT,SAAS,aAAa,KAKgB;QALhB,EACpB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,MAAM,EACb,GAAG,OACiC,GALhB;;IAMpB,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACE,4TAAC,wIAAM;QACL,WAAW,IAAA,qHAAE,EACX,gCACA,gBAAgB,eACZ,uCACA,kDACJ;QAEF,aAAU;QACV,UAAU,CAAC;QACX,SAAS;QACT,MAAM;QACN,SAAS;QACR,GAAG,KAAK;;0BAET,4TAAC,uTAAU;;;;;0BACX,4TAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;IA5BS;;QAM4C;;;MAN5C", "debugId": null}}, {"offset": {"line": 971, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["import Image from 'next/image';\nimport { cn } from '@/lib/utils';\n\nexport const ReviewCard = ({\n  img,\n  name,\n  username,\n  body,\n}: {\n  img: string;\n  name: string;\n  username: string;\n  body: string;\n}) => {\n  return (\n    <figure\n      className={cn(\n        \"group flex max-h-72 w-fit flex-shrink-0 flex-col justify-between gap-8 rounded-xl bg-muted/40 p-4 px-5 opacity-60 shadow-sm transition-all duration-300 hover:bg-[url('/gradient.webp')] hover:bg-center hover:bg-cover hover:bg-no-repeat hover:opacity-100 hover:shadow-md md:w-[28rem] md:p-6 md:px-7 [&:hover_.avatar-ring]:ring-brandYellow [&:hover_.external-link-icon]:opacity-100 [&:hover_.social-icon-border]:border-brand-600\"\n      )}\n    >\n      <blockquote className=\"mt-2 font-medium text-md text-primary tracking-normal\">\n        {body}\n        One of the most delightful, inventive, powerful new interfaces I've\n        tried in years. Actually feels like an AI native computer.\n      </blockquote>\n      <div className=\"relative flex w-full items-center gap-3 text-left transition-all duration-300 hover:scale-[98%]\">\n        <Image\n          alt=\"\"\n          className=\"size-12 rounded-full\"\n          height=\"48\"\n          src={img}\n          width=\"48\"\n        />\n\n        <div className=\"flex-1\">\n          <figcaption className=\"font-aeonik font-medium text-lg text-primary/80 tracking-normal\">\n            {name}\n          </figcaption>\n          <p className=\"font-aeonik font-normal text-md text-primary/50 tracking-tight\">\n            {username}\n          </p>\n        </div>\n      </div>\n    </figure>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEO,MAAM,aAAa;QAAC,EACzB,GAAG,EACH,IAAI,EACJ,QAAQ,EACR,IAAI,EAML;IACC,qBACE,4TAAC;QACC,WAAW,IAAA,qHAAE,EACX;;0BAGF,4TAAC;gBAAW,WAAU;;oBACnB;oBAAK;;;;;;;0BAIR,4TAAC;gBAAI,WAAU;;kCACb,4TAAC,0QAAK;wBACJ,KAAI;wBACJ,WAAU;wBACV,QAAO;wBACP,KAAK;wBACL,OAAM;;;;;;kCAGR,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAW,WAAU;0CACnB;;;;;;0CAEH,4TAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;;;;;;;;;;;;;AAMb;KA1Ca", "debugId": null}}, {"offset": {"line": 1059, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport {\n  Carousel,\n  CarouselContent,\n  CarouselItem,\n  CarouselNext,\n  CarouselPrevious,\n  useCarousel,\n} from '@/components/ui/carousel';\nimport { reviews } from '@/config/docs';\nimport { ReviewCard } from './review-card';\n\nfunction CarouselDots() {\n  const { api } = useCarousel();\n  const [selectedIndex, setSelectedIndex] = useState(0);\n  const [count, setCount] = useState(0);\n\n  useEffect(() => {\n    if (!api) return;\n\n    setCount(api.scrollSnapList().length);\n\n    const onSelect = () => {\n      setSelectedIndex(api.selectedScrollSnap());\n    };\n\n    api.on('select', onSelect);\n    onSelect();\n\n    return () => {\n      api.off('select', onSelect);\n    };\n  }, [api]);\n\n  return (\n    <div className=\"mt-4 flex justify-center gap-2\">\n      {Array.from({ length: count }).map((_, i) => (\n        <button\n          className={`h-2 rounded-full transition-all ${\n            i === selectedIndex ? 'w-8 bg-brand-600' : 'w-2 bg-muted'\n          }`}\n          key={i}\n          onClick={() => api?.scrollTo(i)}\n          type=\"button\"\n        />\n      ))}\n    </div>\n  );\n}\n\nexport default function RevieCardsMobile() {\n  return (\n    <div className=\"md:hidden\">\n      <Carousel>\n        <CarouselContent>\n          {reviews.map((review) => (\n            <CarouselItem key={review.name}>\n              <ReviewCard {...review} />\n            </CarouselItem>\n          ))}\n        </CarouselContent>\n        <CarouselPrevious />\n        <CarouselNext />\n        <CarouselDots />\n      </Carousel>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAQA;AACA;;;AAZA;;;;;AAcA,SAAS;;IACP,MAAM,EAAE,GAAG,EAAE,GAAG,IAAA,+IAAW;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,wSAAQ,EAAC;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,wSAAQ,EAAC;IAEnC,IAAA,ySAAS;kCAAC;YACR,IAAI,CAAC,KAAK;YAEV,SAAS,IAAI,cAAc,GAAG,MAAM;YAEpC,MAAM;mDAAW;oBACf,iBAAiB,IAAI,kBAAkB;gBACzC;;YAEA,IAAI,EAAE,CAAC,UAAU;YACjB;YAEA;0CAAO;oBACL,IAAI,GAAG,CAAC,UAAU;gBACpB;;QACF;iCAAG;QAAC;KAAI;IAER,qBACE,4TAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,kBACrC,4TAAC;gBACC,WAAW,AAAC,mCAEX,OADC,MAAM,gBAAgB,qBAAqB;gBAG7C,SAAS,IAAM,gBAAA,0BAAA,IAAK,QAAQ,CAAC;gBAC7B,MAAK;eAFA;;;;;;;;;;AAOf;GApCS;;QACS,+IAAW;;;KADpB;AAsCM,SAAS;IACtB,qBACE,4TAAC;QAAI,WAAU;kBACb,cAAA,4TAAC,4IAAQ;;8BACP,4TAAC,mJAAe;8BACb,4HAAO,CAAC,GAAG,CAAC,CAAC,uBACZ,4TAAC,gJAAY;sCACX,cAAA,4TAAC,oJAAU;gCAAE,GAAG,MAAM;;;;;;2BADL,OAAO,IAAI;;;;;;;;;;8BAKlC,4TAAC,oJAAgB;;;;;8BACjB,4TAAC,gJAAY;;;;;8BACb,4TAAC;;;;;;;;;;;;;;;;AAIT;MAjBwB", "debugId": null}}]}