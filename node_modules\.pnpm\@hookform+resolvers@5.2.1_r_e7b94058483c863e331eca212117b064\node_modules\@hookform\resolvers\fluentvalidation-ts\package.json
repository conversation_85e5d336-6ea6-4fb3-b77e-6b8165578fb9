{"name": "@hookform/resolvers/fluentvalidation-ts", "amdName": "hookformResolversfluentvalidation-ts", "version": "1.0.0", "private": true, "description": "React Hook Form validation resolver: fluentvalidation-ts", "main": "dist/fluentvalidation-ts.js", "module": "dist/fluentvalidation-ts.module.js", "umd:main": "dist/fluentvalidation-ts.umd.js", "source": "src/index.ts", "types": "dist/index.d.ts", "license": "MIT", "peerDependencies": {"react-hook-form": "^7.55.0", "@hookform/resolvers": "^2.0.0", "fluentvalidation-ts": "^3.0.0"}}