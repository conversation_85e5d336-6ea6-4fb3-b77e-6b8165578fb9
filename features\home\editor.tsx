import Image from 'next/image';
import { ImageZoom } from '@/components/ui/kibo-ui/image-zoom';
import { cn } from '@/lib/utils';

/** biome-ignore-all lint/performance/noImgElement: <explanation> */
export default function EditorFeatureHome() {
  return (
    <section className="mb-40">
      <div className="mx-auto max-w-6xl px-0 md:px-10 2xl:max-w-7xl">
        <div className="flex flex-col items-center gap-[80px] font-aeonik">
          {/* Desktop */}
          <div className="flex w-full flex-col items-start gap-[60px]">
            <div className="flex w-full flex-col items-start gap-5">
              <div className="font-medium text-brand-500 text-xl">
                Desktop Intelligence
              </div>
              <h2 className="max-w-[320px] font-aeonik font-medium text-3xl text-white/80 leading-none tracking-[-1px] sm:max-w-2xl md:max-w-none md:text-[42px]">
                <PERSON><PERSON> understands what you see and hear.
              </h2>
            </div>
            <a
              className="group relative flex aspect-square w-full cursor-pointer items-center justify-center overflow-hidden rounded-[20px] bg-[#303030] md:aspect-auto"
              href="/chat"
            >
              <img
                alt="Intelligence Hero"
                className="h-full w-full rounded-[20px] object-cover transition-transform duration-300 group-hover:scale-105 md:h-auto md:object-contain"
                height="622"
                src="/desktop.webp"
                width="1200"
              />
            </a>
          </div>
          {/* grids for more */}
          <div className="grid w-full grid-cols-1 items-start gap-16 lg:grid-cols-2">
            <div className="flex flex-1 flex-col gap-[50px]">
              <div className="flex flex-col gap-3">
                <h3 className="font-aeonik font-medium text-2xl text-white leading-none tracking-[-1.2px] md:text-[30px]">
                  Record Meetings
                </h3>
                <p className="mx-auto mb-2 max-w-3xl font-aeonik text-lg text-muted-foreground sm:text-xl">
                  <span>
                    Record meetings, lessons, interviews, and more. Instant
                    transcripts &amp; summaries.
                  </span>
                </p>
                <a
                  className="flex items-center gap-2 font-medium text-[16px] text-brand-500 transition-colors hover:text-brand-500/80"
                  href="/meetings"
                >
                  Learn More
                  <svg
                    className="lucide lucide-chevron-right h-4 w-4"
                    fill="none"
                    height="24"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>Right Arrow</title>
                    <path d="m9 18 6-6-6-6" />
                  </svg>
                </a>
              </div>
              <div className="flex flex-col gap-2 md:gap-[25px]">
                <div className="group relative flex h-[450px] w-full cursor-pointer items-center justify-center overflow-hidden rounded-[20px] bg-[#303030]">
                  <ImageZoom
                    backdropClassName={cn(
                      '[&_[data-rmiz-modal-overlay="visible"]]:bg-brand-100/80'
                    )}
                    className="relative aspect-square h-full w-full overflow-hidden rounded-lg"
                    zoomMargin={100}
                  >
                    <Image
                      alt="Recording interface"
                      className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                      height="450"
                      loading="lazy"
                      sizes="100vw"
                      src="/desktop.webp"
                      style={{
                        position: 'absolute',
                        height: '100%',
                        width: '100%',
                        inset: '0px',
                        color: 'transparent',
                      }}
                      width="450"
                    />
                  </ImageZoom>
                </div>
                <div className="flex flex-col gap-3 px-3 py-2 md:flex-row md:items-center md:justify-between md:gap-0">
                  <span className="font-aeonik font-medium text-base text-white/50 tracking-[-0.4px] md:text-[20px]">
                    Works with all your meetings platforms.
                  </span>
                </div>
              </div>
            </div>
            <div className="flex flex-1 flex-col gap-[50px]">
              <div className="flex flex-col gap-3">
                <h3 className="font-aeonik font-medium text-2xl text-white leading-none tracking-[-1.2px] md:text-[30px]">
                  Record Meetings
                </h3>
                <p className="mx-auto mb-2 max-w-3xl font-aeonik text-lg text-muted-foreground sm:text-xl">
                  <span>
                    Record meetings, lessons, interviews, and more. Instant
                    transcripts &amp; summaries.
                  </span>
                </p>
                <a
                  className="flex items-center gap-2 font-medium text-[16px] text-brand-500 transition-colors hover:text-brand-500/80"
                  href="/meetings"
                >
                  Learn More
                  <svg
                    className="lucide lucide-chevron-right h-4 w-4"
                    fill="none"
                    height="24"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>Right Arrow</title>
                    <path d="m9 18 6-6-6-6" />
                  </svg>
                </a>
              </div>
              <div className="flex flex-col gap-2 md:gap-[25px]">
                <div className="group relative flex h-[450px] w-full cursor-pointer items-center justify-center overflow-hidden rounded-[20px] bg-[#303030]">
                  <ImageZoom
                    backdropClassName={cn(
                      '[&_[data-rmiz-modal-overlay="visible"]]:bg-brand-100/80'
                    )}
                    className="relative aspect-square h-full w-full overflow-hidden rounded-lg"
                    zoomMargin={100}
                  >
                    <Image
                      alt="Recording interface"
                      className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                      height="450"
                      loading="lazy"
                      sizes="100vw"
                      src="/desktop.webp"
                      style={{
                        position: 'absolute',
                        height: '100%',
                        width: '100%',
                        inset: '0px',
                        color: 'transparent',
                      }}
                      width="450"
                    />
                  </ImageZoom>
                </div>
                <div className="flex flex-col gap-3 px-3 py-2 md:flex-row md:items-center md:justify-between md:gap-0">
                  <span className="font-aeonik font-medium text-base text-white/50 tracking-[-0.4px] md:text-[20px]">
                    Works with all your meetings platforms.
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
