{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/emails/subscribe-template.tsx"], "sourcesContent": ["import {\r\n  <PERSON>,\r\n  <PERSON><PERSON>,\r\n  Column,\r\n  Con<PERSON>er,\r\n  <PERSON><PERSON>,\r\n  <PERSON>,\r\n  Hr,\r\n  Html,\r\n  Link,\r\n  Preview,\r\n  Row,\r\n  Section,\r\n  Tailwind,\r\n  Text,\r\n} from '@react-email/components';\r\n\r\ntype EmailTemplateProps= {\r\n  email: string;\r\n}\r\n\r\nexport const SubscribedTemplate = ({ email }: EmailTemplateProps) => {\r\n  const currentDate = new Date().toLocaleDateString('en-US', {\r\n    year: 'numeric',\r\n    month: 'long',\r\n    day: 'numeric',\r\n    hour: '2-digit',\r\n    minute: '2-digit',\r\n  });\r\n\r\n  // Ensure the email is displayed safely\r\n  const safeEmail = email || '[No email provided]';\r\n\r\n  return (\r\n    <Html>\r\n      <Head>\r\n        <Font\r\n          fallbackFontFamily=\"Arial\"\r\n          fontFamily=\"Roboto\"\r\n          fontStyle=\"normal\"\r\n          fontWeight={400}\r\n          webFont={{\r\n            url: 'https://fonts.gstatic.com/s/roboto/v27/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2',\r\n            format: 'woff2',\r\n          }}\r\n        />\r\n      </Head>\r\n      <Preview>\r\n        New subscriber: {safeEmail} has joined your mailing list!\r\n      </Preview>\r\n      <Tailwind>\r\n        <Body className=\"mx-auto my-auto bg-gray-50 font-sans\">\r\n          <Container className=\"mx-auto my-8 max-w-[600px]\">\r\n            {/* Header */}\r\n            <Section className=\"rounded-t-lg bg-blue-600 px-8 py-6\">\r\n              <Row>\r\n                <Column>\r\n                  <Text className=\"text-center font-bold text-3xl text-white\">\r\n                    Rathon\r\n                  </Text>\r\n                </Column>\r\n              </Row>\r\n            </Section>\r\n\r\n            {/* Main Content */}\r\n            <Section className=\"rounded-b-lg bg-white px-8 py-10 shadow-sm\">\r\n              <Row>\r\n                <Column>\r\n                  <Text className=\"mb-5 font-bold text-2xl text-gray-800\">\r\n                    New Subscriber Alert\r\n                  </Text>\r\n\r\n                  <Text className=\"mb-5 text-gray-700\">\r\n                    You have a new subscriber to your mailing list! Someone is\r\n                    interested in hearing more about Rathon&apos;s services and\r\n                    updates.\r\n                  </Text>\r\n\r\n                  {/* Subscriber Info */}\r\n                  <Container className=\"mb-6 rounded-md border-blue-500 border-l-4 bg-blue-50 p-5\">\r\n                    <Text className=\"mb-1 font-medium text-base text-blue-800\">\r\n                      Subscriber Details:\r\n                    </Text>\r\n                    <Text className=\"mb-0 text-blue-800\">\r\n                      <strong>Email:</strong> {safeEmail}\r\n                    </Text>\r\n                    <Text className=\"mb-0 text-blue-800\">\r\n                      <strong>Subscribed on:</strong> {currentDate}\r\n                    </Text>\r\n                  </Container>\r\n\r\n                  {/* Action Button */}\r\n                  <Section className=\"mb-8 text-center\">\r\n                    <Button\r\n                      className=\"rounded-md bg-blue-600 px-6 py-3 font-medium text-white hover:bg-blue-700\"\r\n                      href={`mailto:${safeEmail}`}\r\n                    >\r\n                      Contact Subscriber\r\n                    </Button>\r\n                  </Section>\r\n\r\n                  <Hr className=\"my-6 border-gray-200\" />\r\n\r\n                  <Text className=\"mb-4 text-gray-700\">\r\n                    Remember to maintain GDPR compliance when reaching out to\r\n                    new subscribers.\r\n                  </Text>\r\n                </Column>\r\n              </Row>\r\n            </Section>\r\n\r\n            {/* Footer */}\r\n            <Section className=\"px-8 py-6\">\r\n              <Text className=\"text-center text-gray-500 text-xs\">\r\n                © {new Date().getFullYear()} Rathon. All rights reserved.\r\n              </Text>\r\n              <Text className=\"text-center text-gray-500 text-xs\">\r\n                This is an automated notification from your website&apos;s\r\n                subscription system.\r\n              </Text>\r\n              <Text className=\"text-center text-gray-500 text-xs\">\r\n                <Link className=\"text-blue-500 underline\" href=\"#\">\r\n                  Unsubscribe\r\n                </Link>{' '}\r\n                from these notifications\r\n              </Text>\r\n            </Section>\r\n          </Container>\r\n        </Body>\r\n      </Tailwind>\r\n    </Html>\r\n  );\r\n};"], "names": [], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAqBO,MAAM,qBAAqB,CAAC,EAAE,KAAK,EAAsB;IAC9D,MAAM,cAAc,IAAI,OAAO,kBAAkB,CAAC,SAAS;QACzD,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;IAEA,uCAAuC;IACvC,MAAM,YAAY,SAAS;IAE3B,qBACE,6WAAC,8PAAI;;0BACH,6WAAC,8PAAI;0BACH,cAAA,6WAAC,6PAAI;oBACH,oBAAmB;oBACnB,YAAW;oBACX,WAAU;oBACV,YAAY;oBACZ,SAAS;wBACP,KAAK;wBACL,QAAQ;oBACV;;;;;;;;;;;0BAG<PERSON>,6WAAC,uQAAO;;oBAAC;oBACU;oBAAU;;;;;;;0BAE7B,6WAAC,yQAAQ;0BACP,cAAA,6WAAC,6PAAI;oBAAC,WAAU;8BACd,cAAA,6WAAC,6QAAS;wBAAC,WAAU;;0CAEnB,6WAAC,uQAAO;gCAAC,WAAU;0CACjB,cAAA,6WAAC,2PAAG;8CACF,cAAA,6WAAC,oQAAM;kDACL,cAAA,6WAAC,6PAAI;4CAAC,WAAU;sDAA4C;;;;;;;;;;;;;;;;;;;;;0CAQlE,6WAAC,uQAAO;gCAAC,WAAU;0CACjB,cAAA,6WAAC,2PAAG;8CACF,cAAA,6WAAC,oQAAM;;0DACL,6WAAC,6PAAI;gDAAC,WAAU;0DAAwC;;;;;;0DAIxD,6WAAC,6PAAI;gDAAC,WAAU;0DAAqB;;;;;;0DAOrC,6WAAC,6QAAS;gDAAC,WAAU;;kEACnB,6WAAC,6PAAI;wDAAC,WAAU;kEAA2C;;;;;;kEAG3D,6WAAC,6PAAI;wDAAC,WAAU;;0EACd,6WAAC;0EAAO;;;;;;4DAAe;4DAAE;;;;;;;kEAE3B,6WAAC,6PAAI;wDAAC,WAAU;;0EACd,6WAAC;0EAAO;;;;;;4DAAuB;4DAAE;;;;;;;;;;;;;0DAKrC,6WAAC,uQAAO;gDAAC,WAAU;0DACjB,cAAA,6WAAC,mQAAM;oDACL,WAAU;oDACV,MAAM,CAAC,OAAO,EAAE,WAAW;8DAC5B;;;;;;;;;;;0DAKH,6WAAC,wPAAE;gDAAC,WAAU;;;;;;0DAEd,6WAAC,6PAAI;gDAAC,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;;;;;0CAS3C,6WAAC,uQAAO;gCAAC,WAAU;;kDACjB,6WAAC,6PAAI;wCAAC,WAAU;;4CAAoC;4CAC/C,IAAI,OAAO,WAAW;4CAAG;;;;;;;kDAE9B,6WAAC,6PAAI;wCAAC,WAAU;kDAAoC;;;;;;kDAIpD,6WAAC,6PAAI;wCAAC,WAAU;;0DACd,6WAAC,8PAAI;gDAAC,WAAU;gDAA0B,MAAK;0DAAI;;;;;;4CAE3C;4CAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5B", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/server/subscribe.action.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { Resend } from \"resend\";\r\nimport { SubscribedTemplate } from \"@/components/emails/subscribe-template\";\r\nimport type { TSubFormSchema } from \"./schema\";\r\n\r\n// Initialize Resend client with API key from environment variables\r\nconst resendClient = new Resend(process.env.RESEND_API_KEY);\r\n\r\nexport async function subscribe(formData: TSubFormSchema) {\r\n  try {\r\n    const { email } = formData;\r\n\r\n    const response = await resendClient.emails.send({\r\n      from: \"Rathon <<EMAIL>>\",\r\n      to: [\"<EMAIL>\"],\r\n      subject: \"New Subscriber from Rathon Website\",\r\n      react: SubscribedTemplate({ email }) as React.ReactElement,\r\n      replyTo: email,\r\n      tags: [{ name: \"source\", value: \"website_subscribe\" }],\r\n    });\r\n\r\n    if (response.error) {\r\n      return { success: false, error: response.error };\r\n    }\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    return { success: false, error };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;;;AAGA,mEAAmE;AACnE,MAAM,eAAe,IAAI,8PAAM,CAAC,QAAQ,GAAG,CAAC,cAAc;AAEnD,eAAe,UAAU,QAAwB;IACtD,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG;QAElB,MAAM,WAAW,MAAM,aAAa,MAAM,CAAC,IAAI,CAAC;YAC9C,MAAM;YACN,IAAI;gBAAC;aAAqB;YAC1B,SAAS;YACT,OAAO,IAAA,oKAAkB,EAAC;gBAAE;YAAM;YAClC,SAAS;YACT,MAAM;gBAAC;oBAAE,MAAM;oBAAU,OAAO;gBAAoB;aAAE;QACxD;QAEA,IAAI,SAAS,KAAK,EAAE;YAClB,OAAO;gBAAE,SAAS;gBAAO,OAAO,SAAS,KAAK;YAAC;QACjD;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;;;IArBsB;;AAAA,8WAAA", "debugId": null}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/.next-internal/server/app/%28root%29/pricing/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {subscribe as '401553368a882499aede4e05ebfdb3ca7882231805'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/accordion.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Accordion = registerClientReference(\n    function() { throw new Error(\"Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx <module evaluation>\",\n    \"Accordion\",\n);\nexport const AccordionContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx <module evaluation>\",\n    \"AccordionContent\",\n);\nexport const AccordionItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx <module evaluation>\",\n    \"AccordionItem\",\n);\nexport const AccordionTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx <module evaluation>\",\n    \"AccordionTrigger\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,uYAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA;AAEG,MAAM,mBAAmB,IAAA,uYAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6DACA;AAEG,MAAM,gBAAgB,IAAA,uYAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,6DACA;AAEG,MAAM,mBAAmB,IAAA,uYAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/accordion.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Accordion = registerClientReference(\n    function() { throw new Error(\"Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx\",\n    \"Accordion\",\n);\nexport const AccordionContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx\",\n    \"AccordionContent\",\n);\nexport const AccordionItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx\",\n    \"AccordionItem\",\n);\nexport const AccordionTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx\",\n    \"AccordionTrigger\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,uYAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA;AAEG,MAAM,mBAAmB,IAAA,uYAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,yCACA;AAEG,MAAM,gBAAgB,IAAA,uYAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yCACA;AAEG,MAAM,mBAAmB,IAAA,uYAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,yCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 451, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["import {\r\n  Accordion,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@/components/ui/accordion';\r\n\r\nconst faqs = [\r\n  {\r\n    question: 'Does Highlight work on my device?',\r\n    answer:\r\n      \"We are the only AI assistant that works across both Windows and Mac. Our team is fully committed to developing a product that meets users wherever they're at.\",\r\n  },\r\n  {\r\n    question: 'How is my data handled?',\r\n    answer:\r\n      \"We don't see your data. Period. The only time your data leaves your computer is when you attach it to a cloud LLM query or if you had enabled task detection, and then only the cloud LLM - GPT4o, Claude, etc - is able to see your information.\",\r\n  },\r\n  {\r\n    question: 'Is Highlight SOC-2 compliant?',\r\n    answer:\r\n      'We are undergoing the SOC-2 process and expect to complete certification by end of 2025. In the meantime, we can supply and complete any security questionnaires for enterprises interested in working with us, and have published our policies on trust and safety here.',\r\n  },\r\n  {\r\n    question: 'How is this different from ChatGPT?',\r\n    answer:\r\n      \"Highlight is ChatGPT-level intelligence, but with the ability to understand what you're looking at on your screen or have said or heard on your laptop. Think of it like ChatGPT, but without the need to ever explain yourself (plus a bunch of other cool features).\",\r\n  },\r\n  {\r\n    question: 'Do you have a mobile app?',\r\n    answer: \"Not yet, but we're working on it!\",\r\n  },\r\n];\r\n\r\nexport default function FaqsHome() {\r\n  return (\r\n    <section className=\"mb-40\">\r\n      <div className=\"mx-auto max-w-6xl px-0 md:px-10 2xl:max-w-7xl\">\r\n        <h2\r\n          className=\"mx-auto mb-14 max-w-[320px] whitespace-pre-wrap text-center font-aeonik-bold font-medium text-3xl leading-tight sm:max-w-2xl sm:text-4xl md:max-w-none md:text-4xl lg:text-5xl\"\r\n          id=\"faq-heading\"\r\n        >\r\n          Common Questions\r\n        </h2>\r\n        <div className=\"h-full pb-8\">\r\n          <Accordion collapsible type=\"single\">\r\n            {faqs.map((faq, index) => (\r\n              <AccordionItem key={index} value={`item-${index}`}>\r\n                <AccordionTrigger className=\"cursor-pointer font-medium text-base text-primary hover:text-primary/80 hover:no-underline\">\r\n                  {faq.question}\r\n                </AccordionTrigger>\r\n                <AccordionContent className=\"font-aeonik text-lg text-muted-foreground\">\r\n                  {faq.answer}\r\n                </AccordionContent>\r\n              </AccordionItem>\r\n            ))}\r\n          </Accordion>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAOA,MAAM,OAAO;IACX;QACE,UAAU;QACV,QACE;IACJ;IACA;QACE,UAAU;QACV,QACE;IACJ;IACA;QACE,UAAU;QACV,QACE;IACJ;IACA;QACE,UAAU;QACV,QACE;IACJ;IACA;QACE,UAAU;QACV,QAAQ;IACV;CACD;AAEc,SAAS;IACtB,qBACE,6WAAC;QAAQ,WAAU;kBACjB,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBACC,WAAU;oBACV,IAAG;8BACJ;;;;;;8BAGD,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC,2IAAS;wBAAC,WAAW;wBAAC,MAAK;kCACzB,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6WAAC,+IAAa;gCAAa,OAAO,CAAC,KAAK,EAAE,OAAO;;kDAC/C,6WAAC,kJAAgB;wCAAC,WAAU;kDACzB,IAAI,QAAQ;;;;;;kDAEf,6WAAC,kJAAgB;wCAAC,WAAU;kDACzB,IAAI,MAAM;;;;;;;+BALK;;;;;;;;;;;;;;;;;;;;;;;;;;AAclC", "debugId": null}}, {"offset": {"line": 552, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/pricing/hero.tsx"], "sourcesContent": ["export default function PricingHero() {\r\n  return (\r\n    <section className=\"relative mb-16 flex flex-col justify-center sm:mb-20\">\r\n      <div className=\"grid grid-cols-1 items-center gap-8\">\r\n        <div className=\"flex flex-col items-center justify-center text-center\">\r\n          <div className=\"flex w-full flex-col gap-6\">\r\n            <h1 className=\"inline-block break-words font-aeonik font-medium text-[3rem] text-light-80 tracking-[-0.00em] md:leading-[1.25em]\">\r\n              Pricing\r\n            </h1>\r\n          </div>\r\n          <p className=\"mt-4 max-w-3xl whitespace-pre-line text-center font-normal text-lg text-muted-foreground sm:text-xl lg:text-xl\">\r\n            Pick the plan that's right for you.\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,6WAAC;QAAQ,WAAU;kBACjB,cAAA,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAG,WAAU;sCAAoH;;;;;;;;;;;kCAIpI,6WAAC;wBAAE,WAAU;kCAAiH;;;;;;;;;;;;;;;;;;;;;;AAOxI", "debugId": null}}, {"offset": {"line": 610, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/pricing/parteners.tsx"], "sourcesContent": ["/** biome-ignore-all lint/nursery/useImageSize: <explanation> */\r\n/** biome-ignore-all lint/performance/noImgElement: <explanation> */\r\nexport default function Parteners() {\r\n  return (\r\n    <section>\r\n      <div className=\"mb-2 flex flex-col items-center font-aeonik\">\r\n        <h2 className=\"text-center font-medium text-lg text-muted-foreground/50\">\r\n          Backed by our Partners at\r\n        </h2>\r\n      </div>\r\n      <div className=\"mx-auto mb-[160px] grid max-w-2xl grid-cols-2 gap-4 rounded-xl\">\r\n        <div className=\"relative flex h-32 items-center justify-center\">\r\n          <a\r\n            className=\"relative flex h-full w-full items-center justify-center\"\r\n            href=\"https://www.generalcatalyst.com/\"\r\n            rel=\"noopener noreferrer\"\r\n            target=\"_blank\"\r\n          >\r\n            <img\r\n              alt=\"Logo 1\"\r\n              className=\"max-h-32 max-w-full object-contain\"\r\n              src=\"https://cdn.highlightai.com/media/landing/misc/backed-by/general-catalyst-logo.webp\"\r\n            />\r\n          </a>\r\n        </div>\r\n        <div className=\"relative flex h-32 items-center justify-center\">\r\n          <a\r\n            className=\"relative flex h-full w-full items-center justify-center\"\r\n            href=\"https://www.generalcatalyst.com/\"\r\n            rel=\"noopener noreferrer\"\r\n            target=\"_blank\"\r\n          >\r\n            <img\r\n              alt=\"Logo 1\"\r\n              className=\"max-h-32 max-w-full object-contain\"\r\n              src=\"https://cdn.highlightai.com/media/landing/misc/backed-by/general-catalyst-logo.webp\"\r\n            />\r\n          </a>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,8DAA8D,GAC9D,kEAAkE;;;;;;AACnD,SAAS;IACtB,qBACE,6WAAC;;0BACC,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAG,WAAU;8BAA2D;;;;;;;;;;;0BAI3E,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BACC,WAAU;4BACV,MAAK;4BACL,KAAI;4BACJ,QAAO;sCAEP,cAAA,6WAAC;gCACC,KAAI;gCACJ,WAAU;gCACV,KAAI;;;;;;;;;;;;;;;;kCAIV,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BACC,WAAU;4BACV,MAAK;4BACL,KAAI;4BACJ,QAAO;sCAEP,cAAA,6WAAC;gCACC,KAAI;gCACJ,WAAU;gCACV,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}, {"offset": {"line": 706, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/pricing/pricing-cards.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/features/pricing/pricing-cards.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/features/pricing/pricing-cards.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,uYAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 720, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/pricing/pricing-cards.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/features/pricing/pricing-cards.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/features/pricing/pricing-cards.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,uYAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 734, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 742, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/app/%28root%29/pricing/page.tsx"], "sourcesContent": ["import FaqsHome from '@/features/home/<USER>';\r\nimport PricingHero from '@/features/pricing/hero';\r\nimport Parteners from '@/features/pricing/parteners';\r\nimport PricingCards from '@/features/pricing/pricing-cards';\r\n\r\nexport default function PricingPage() {\r\n  return (\r\n    <main className=\"pt-[90px]\">\r\n      <div className=\"mx-auto max-w-7xl px-0 pt-4 sm:px-6 sm:pt-16 md:px-4 lg:px-8\">\r\n        <PricingHero />\r\n        <PricingCards />\r\n        <Parteners />\r\n        <FaqsHome />\r\n      </div>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEe,SAAS;IACtB,qBACE,6WAAC;QAAK,WAAU;kBACd,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC,uIAAW;;;;;8BACZ,6WAAC,mJAAY;;;;;8BACb,6WAAC,4IAAS;;;;;8BACV,6WAAC,oIAAQ;;;;;;;;;;;;;;;;AAIjB", "debugId": null}}]}