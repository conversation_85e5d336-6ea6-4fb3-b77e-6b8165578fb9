{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/accordion.tsx"], "sourcesContent": ["'use client';\n\nimport * as AccordionPrimitive from '@radix-ui/react-accordion';\nimport { ChevronDownIcon } from 'lucide-react';\nimport type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Accordion({\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Root>) {\n  return <AccordionPrimitive.Root data-slot=\"accordion\" {...props} />;\n}\n\nfunction AccordionItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Item>) {\n  return (\n    <AccordionPrimitive.Item\n      className={cn('border-b last:border-b-0', className)}\n      data-slot=\"accordion-item\"\n      {...props}\n    />\n  );\n}\n\nfunction AccordionTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {\n  return (\n    <AccordionPrimitive.Header className=\"flex\">\n      <AccordionPrimitive.Trigger\n        className={cn(\n          'flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left font-medium text-sm outline-none transition-all hover:underline focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180',\n          className\n        )}\n        data-slot=\"accordion-trigger\"\n        {...props}\n      >\n        {children}\n        <ChevronDownIcon className=\"pointer-events-none size-4 shrink-0 translate-y-0.5 text-muted-foreground transition-transform duration-200\" />\n      </AccordionPrimitive.Trigger>\n    </AccordionPrimitive.Header>\n  );\n}\n\nfunction AccordionContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Content>) {\n  return (\n    <AccordionPrimitive.Content\n      className=\"overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\n      data-slot=\"accordion-content\"\n      {...props}\n    >\n      <div className={cn('pt-0 pb-4', className)}>{children}</div>\n    </AccordionPrimitive.Content>\n  );\n}\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent };\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AAGA;AANA;;;;;AAQA,SAAS,UAAU,KAEoC;QAFpC,EACjB,GAAG,OACkD,GAFpC;IAGjB,qBAAO,4TAAC,uRAAuB;QAAC,aAAU;QAAa,GAAG,KAAK;;;;;;AACjE;KAJS;AAMT,SAAS,cAAc,KAGgC;QAHhC,EACrB,SAAS,EACT,GAAG,OACkD,GAHhC;IAIrB,qBACE,4TAAC,uRAAuB;QACtB,WAAW,IAAA,qHAAE,EAAC,4BAA4B;QAC1C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,KAIgC;QAJhC,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD,GAJhC;IAKxB,qBACE,4TAAC,yRAAyB;QAAC,WAAU;kBACnC,cAAA,4TAAC,0RAA0B;YACzB,WAAW,IAAA,qHAAE,EACX,8SACA;YAEF,aAAU;YACT,GAAG,KAAK;;gBAER;8BACD,4TAAC,kUAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MApBS;AAsBT,SAAS,iBAAiB,KAIgC;QAJhC,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD,GAJhC;IAKxB,qBACE,4TAAC,0RAA0B;QACzB,WAAU;QACV,aAAU;QACT,GAAG,KAAK;kBAET,cAAA,4TAAC;YAAI,WAAW,IAAA,qHAAE,EAAC,aAAa;sBAAa;;;;;;;;;;;AAGnD;MAdS", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,IAAA,qPAAG,EACvB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,gTAAI,GAAG;IAE9B,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/tabs.tsx"], "sourcesContent": ["'use client';\n\nimport * as TabsPrimitive from '@radix-ui/react-tabs';\nimport type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      className={cn('flex flex-col gap-2', className)}\n      data-slot=\"tabs\"\n      {...props}\n    />\n  );\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      className={cn(\n        'inline-flex h-9 w-fit items-center justify-center rounded-lg bg-muted p-[3px] text-muted-foreground',\n        className\n      )}\n      data-slot=\"tabs-list\"\n      {...props}\n    />\n  );\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      className={cn(\n        \"inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 whitespace-nowrap rounded-md border border-transparent px-2 py-1 font-medium text-foreground text-sm transition-[color,box-shadow] focus-visible:border-ring focus-visible:outline-1 focus-visible:outline-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:shadow-sm dark:text-muted-foreground dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 dark:data-[state=active]:text-foreground [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0\",\n        className\n      )}\n      data-slot=\"tabs-trigger\"\n      {...props}\n    />\n  );\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      className={cn('flex-1 outline-none', className)}\n      data-slot=\"tabs-content\"\n      {...props}\n    />\n  );\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAGA;AALA;;;;AAOA,SAAS,KAAK,KAGoC;QAHpC,EACZ,SAAS,EACT,GAAG,OAC6C,GAHpC;IAIZ,qBACE,4TAAC,wRAAkB;QACjB,WAAW,IAAA,qHAAE,EAAC,uBAAuB;QACrC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,KAGgC;QAHhC,EAChB,SAAS,EACT,GAAG,OAC6C,GAHhC;IAIhB,qBACE,4TAAC,wRAAkB;QACjB,WAAW,IAAA,qHAAE,EACX,uGACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,4TAAC,2RAAqB;QACpB,WAAW,IAAA,qHAAE,EACX,mqBACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,4TAAC,2RAAqB;QACpB,WAAW,IAAA,qHAAE,EAAC,uBAAuB;QACrC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/pricing/pricing-cards.tsx"], "sourcesContent": ["'use client';\r\nimport NumberFlow from '@number-flow/react';\r\nimport { BadgeCheck } from 'lucide-react';\r\nimport { useState } from 'react';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardFooter,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@/components/ui/card';\r\nimport { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { plans } from '@/config/docs';\r\nimport { cn } from '@/lib/utils';\r\n\r\nexport default function PricingCards() {\r\n  const [frequency, setFrequency] = useState<string>('monthly');\r\n  return (\r\n    <div className=\"mb-40 flex flex-col gap-16 text-center\">\r\n      <div className=\"flex flex-col items-center justify-center gap-6\">\r\n        <Tabs defaultValue={frequency} onValueChange={setFrequency}>\r\n          <TabsList className=\"rounded-full bg-my-background p-4 py-6\">\r\n            <TabsTrigger\r\n              className=\"rounded-full p-4 dark:data-[state=active]:bg-muted\"\r\n              value=\"monthly\"\r\n            >\r\n              Monthly\r\n            </TabsTrigger>\r\n            <TabsTrigger\r\n              className=\"rounded-full p-4 dark:data-[state=active]:bg-muted\"\r\n              value=\"yearly\"\r\n            >\r\n              Yearly\r\n              <Badge variant=\"secondary\">20% off</Badge>\r\n            </TabsTrigger>\r\n          </TabsList>\r\n        </Tabs>\r\n        <div className=\"grid w-full grid-cols-1 gap-[15px] overflow-visible sm:grid-cols-2 sm:px-4 md:px-0 lg:grid-cols-3\">\r\n          {plans.map((plan) => (\r\n            <Card\r\n              className={cn(\r\n                'relative w-full text-left',\r\n                plan.popular && 'ring-2 ring-primary'\r\n              )}\r\n              key={plan.id}\r\n            >\r\n              {plan.popular && (\r\n                <Badge className=\"-translate-x-1/2 -translate-y-1/2 absolute top-0 left-1/2 rounded-full\">\r\n                  Popular\r\n                </Badge>\r\n              )}\r\n              <CardHeader>\r\n                <CardTitle className=\"font-medium text-xl\">\r\n                  {plan.name}\r\n                </CardTitle>\r\n                <CardDescription>\r\n                  <p className=\"mb-4\">{plan.description}</p>\r\n                  {typeof plan.price[frequency as keyof typeof plan.price] ===\r\n                  'number' ? (\r\n                    <NumberFlow\r\n                      className=\"font-medium text-foreground text-xl lg:text-3xl\"\r\n                      format={{\r\n                        style: 'currency',\r\n                        currency: 'USD',\r\n                        maximumFractionDigits: 0,\r\n                      }}\r\n                      suffix={'/seat'}\r\n                      value={\r\n                        plan.price[\r\n                          frequency as keyof typeof plan.price\r\n                        ] as number\r\n                      }\r\n                    />\r\n                  ) : (\r\n                    <span className=\"font-medium text-foreground text-xl lg:text-2xl\">\r\n                      {plan.price[frequency as keyof typeof plan.price]}.\r\n                    </span>\r\n                  )}\r\n                </CardDescription>\r\n              </CardHeader>\r\n              <CardContent className=\"grid gap-2\">\r\n                {plan.features.map((feature, index) => (\r\n                  <div\r\n                    className=\"flex items-center gap-2 text-muted-foreground text-sm\"\r\n                    key={index}\r\n                  >\r\n                    <BadgeCheck className=\"size-4 text-brand-500\" />\r\n                    {feature}\r\n                  </div>\r\n                ))}\r\n              </CardContent>\r\n              <CardFooter className=\"mt-auto\">\r\n                <Button\r\n                  className={cn(\r\n                    'w-full rounded-full',\r\n                    plan.popular ? 'bg-brand-600 hover:bg-brand-500' : ''\r\n                  )}\r\n                  size={'lg'}\r\n                  variant={plan.popular ? 'default' : 'secondary'}\r\n                >\r\n                  {plan.cta}\r\n                </Button>\r\n              </CardFooter>\r\n            </Card>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;;;AAhBA;;;;;;;;;;AAkBe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,wSAAQ,EAAS;IACnD,qBACE,4TAAC;QAAI,WAAU;kBACb,cAAA,4TAAC;YAAI,WAAU;;8BACb,4TAAC,oIAAI;oBAAC,cAAc;oBAAW,eAAe;8BAC5C,cAAA,4TAAC,wIAAQ;wBAAC,WAAU;;0CAClB,4TAAC,2IAAW;gCACV,WAAU;gCACV,OAAM;0CACP;;;;;;0CAGD,4TAAC,2IAAW;gCACV,WAAU;gCACV,OAAM;;oCACP;kDAEC,4TAAC,sIAAK;wCAAC,SAAQ;kDAAY;;;;;;;;;;;;;;;;;;;;;;;8BAIjC,4TAAC;oBAAI,WAAU;8BACZ,0HAAK,CAAC,GAAG,CAAC,CAAC,qBACV,4TAAC,oIAAI;4BACH,WAAW,IAAA,qHAAE,EACX,6BACA,KAAK,OAAO,IAAI;;gCAIjB,KAAK,OAAO,kBACX,4TAAC,sIAAK;oCAAC,WAAU;8CAAyE;;;;;;8CAI5F,4TAAC,0IAAU;;sDACT,4TAAC,yIAAS;4CAAC,WAAU;sDAClB,KAAK,IAAI;;;;;;sDAEZ,4TAAC,+IAAe;;8DACd,4TAAC;oDAAE,WAAU;8DAAQ,KAAK,WAAW;;;;;;gDACpC,OAAO,KAAK,KAAK,CAAC,UAAqC,KACxD,yBACE,4TAAC,8UAAU;oDACT,WAAU;oDACV,QAAQ;wDACN,OAAO;wDACP,UAAU;wDACV,uBAAuB;oDACzB;oDACA,QAAQ;oDACR,OACE,KAAK,KAAK,CACR,UACD;;;;;yEAIL,4TAAC;oDAAK,WAAU;;wDACb,KAAK,KAAK,CAAC,UAAqC;wDAAC;;;;;;;;;;;;;;;;;;;8CAK1D,4TAAC,2IAAW;oCAAC,WAAU;8CACpB,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,4TAAC;4CACC,WAAU;;8DAGV,4TAAC,uTAAU;oDAAC,WAAU;;;;;;gDACrB;;2CAHI;;;;;;;;;;8CAOX,4TAAC,0IAAU;oCAAC,WAAU;8CACpB,cAAA,4TAAC,wIAAM;wCACL,WAAW,IAAA,qHAAE,EACX,uBACA,KAAK,OAAO,GAAG,oCAAoC;wCAErD,MAAM;wCACN,SAAS,KAAK,OAAO,GAAG,YAAY;kDAEnC,KAAK,GAAG;;;;;;;;;;;;2BAxDR,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;AAiE1B;GA9FwB;KAAA", "debugId": null}}]}