{"version": 3, "file": "ajv.umd.js", "sources": ["../src/ajv.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport Ajv, { DefinedError } from 'ajv';\nimport ajvErrors from 'ajv-errors';\nimport addFormats from 'ajv-formats';\nimport { FieldError, appendErrors } from 'react-hook-form';\nimport { AjvError, Resolver } from './types';\n\nconst parseErrorSchema = (\n  ajvErrors: AjvError[],\n  validateAllFieldCriteria: boolean,\n) => {\n  const parsedErrors: Record<string, FieldError> = {};\n\n  const reduceError = (error: AjvError) => {\n    // Ajv will return empty instancePath when require error\n    if (error.keyword === 'required') {\n      error.instancePath += `/${error.params.missingProperty}`;\n    }\n\n    // `/deepObject/data` -> `deepObject.data`\n    const path = error.instancePath.substring(1).replace(/\\//g, '.');\n\n    if (!parsedErrors[path]) {\n      parsedErrors[path] = {\n        message: error.message,\n        type: error.keyword,\n      };\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = parsedErrors[path].types;\n      const messages = types && types[error.keyword];\n\n      parsedErrors[path] = appendErrors(\n        path,\n        validateAllFieldCriteria,\n        parsedErrors,\n        error.keyword,\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message || '')\n          : error.message,\n      ) as FieldError;\n    }\n  };\n\n  for (let index = 0; index < ajvErrors.length; index += 1) {\n    const error = ajvErrors[index];\n\n    if (error.keyword === 'errorMessage') {\n      error.params.errors.forEach((originalError) => {\n        originalError.message = error.message;\n        reduceError(originalError);\n      });\n    } else {\n      reduceError(error);\n    }\n  }\n\n  return parsedErrors;\n};\n\n/**\n * Creates a resolver for react-hook-form using Ajv schema validation\n * @param {Schema} schema - The Ajv schema to validate against\n * @param {Object} schemaOptions - Additional schema validation options\n * @param {Object} resolverOptions - Additional resolver configuration\n * @param {string} [resolverOptions.mode='async'] - Validation mode\n * @returns {Resolver<Schema>} A resolver function compatible with react-hook-form\n * @example\n * const schema = ajv.compile({\n *   type: 'object',\n *   properties: {\n *     name: { type: 'string' },\n *     age: { type: 'number' }\n *   }\n * });\n *\n * useForm({\n *   resolver: ajvResolver(schema)\n * });\n */\nexport const ajvResolver: Resolver =\n  (schema, schemaOptions, resolverOptions = {}) =>\n  async (values, _, options) => {\n    const ajv = new Ajv(\n      Object.assign(\n        {},\n        {\n          allErrors: true,\n          validateSchema: true,\n        },\n        schemaOptions,\n      ),\n    );\n\n    ajvErrors(ajv);\n    addFormats(ajv);\n\n    const validate = ajv.compile(\n      Object.assign(\n        { $async: resolverOptions && resolverOptions.mode === 'async' },\n        schema,\n      ),\n    );\n\n    const valid = validate(values);\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return valid\n      ? { values, errors: {} }\n      : {\n          values: {},\n          errors: toNestErrors(\n            parseErrorSchema(\n              validate.errors as DefinedError[],\n              !options.shouldUseNativeValidation &&\n                options.criteriaMode === 'all',\n            ),\n            options,\n          ),\n        };\n  };\n"], "names": ["parseErrorSchema", "ajvErrors", "validateAllFieldCriteria", "parsedErrors", "reduceError", "error", "keyword", "instancePath", "params", "missingProperty", "path", "substring", "replace", "message", "type", "types", "messages", "appendErrors", "concat", "_loop", "index", "errors", "for<PERSON>ach", "originalError", "length", "schema", "schemaOptions", "resolverOptions", "values", "_", "options", "ajv", "Ajv", "Object", "assign", "allErrors", "validateSchema", "addFormats", "validate", "compile", "$async", "mode", "valid", "shouldUseNativeValidation", "validateFieldsNatively", "Promise", "resolve", "toNestErrors", "criteriaMode", "e", "reject"], "mappings": "koBAOMA,EAAmB,SACvBC,EACAC,GAoCA,IAlCA,IAAMC,EAA2C,CAAA,EAE3CC,EAAc,SAACC,GAEG,aAAlBA,EAAMC,UACRD,EAAME,cAAoBF,IAAAA,EAAMG,OAAOC,iBAIzC,IAAMC,EAAOL,EAAME,aAAaI,UAAU,GAAGC,QAAQ,MAAO,KAS5D,GAPKT,EAAaO,KAChBP,EAAaO,GAAQ,CACnBG,QAASR,EAAMQ,QACfC,KAAMT,EAAMC,UAIZJ,EAA0B,CAC5B,IAAMa,EAAQZ,EAAaO,GAAMK,MAC3BC,EAAWD,GAASA,EAAMV,EAAMC,SAEtCH,EAAaO,GAAQO,EAAAA,aACnBP,EACAR,EACAC,EACAE,EAAMC,QACNU,EACK,GAAgBE,OAAOF,EAAsBX,EAAMQ,SAAW,IAC/DR,EAAMQ,QAEd,CACF,EAAEM,EAAAA,WAGA,IAAMd,EAAQJ,EAAUmB,GAEF,iBAAlBf,EAAMC,QACRD,EAAMG,OAAOa,OAAOC,QAAQ,SAACC,GAC3BA,EAAcV,QAAUR,EAAMQ,QAC9BT,EAAYmB,EACd,GAEAnB,EAAYC,EAEhB,EAXSe,EAAQ,EAAGA,EAAQnB,EAAUuB,OAAQJ,GAAS,EAACD,IAaxD,OAAOhB,CACT,gBAuBE,SAACsB,EAAQC,EAAeC,GACjBC,YADiBD,IAAAA,IAAAA,EAAkB,IACnCC,SAAAA,EAAQC,EAAGC,OAChB,IAAMC,EAAM,IAAIC,EAAG,QACjBC,OAAOC,OACL,CAAA,EACA,CACEC,WAAW,EACXC,gBAAgB,GAElBV,IAIJzB,UAAU8B,GACVM,EAAU,QAACN,GAEX,IAAMO,EAAWP,EAAIQ,QACnBN,OAAOC,OACL,CAAEM,OAAQb,GAA4C,UAAzBA,EAAgBc,MAC7ChB,IAIEiB,EAAQJ,EAASV,GAIvB,OAFAE,EAAQa,2BAA6BC,EAAsBA,uBAAC,CAAE,EAAEd,GAEhEe,QAAAC,QAAOJ,EACH,CAAEd,OAAAA,EAAQP,OAAQ,CAAI,GACtB,CACEO,OAAQ,CAAA,EACRP,OAAQ0B,EAAYA,aAClB/C,EACEsC,EAASjB,QACRS,EAAQa,2BACkB,QAAzBb,EAAQkB,cAEZlB,IAGV,CAAC,MAAAmB,GAAA,OAAAJ,QAAAK,OAAAD,EAAA,CAAA,CAAA"}