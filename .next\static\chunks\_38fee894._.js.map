{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,uOAAO,EAAC,IAAA,iMAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/separator.tsx"], "sourcesContent": ["'use client';\n\nimport * as SeparatorPrimitive from '@radix-ui/react-separator';\nimport type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Separator({\n  className,\n  orientation = 'horizontal',\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      className={cn(\n        'shrink-0 bg-border data-[orientation=horizontal]:h-px data-[orientation=vertical]:h-full data-[orientation=horizontal]:w-full data-[orientation=vertical]:w-px',\n        className\n      )}\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      {...props}\n    />\n  );\n}\n\nexport { Separator };\n"], "names": [], "mappings": ";;;;;AAEA;AAGA;AALA;;;;AAOA,SAAS,UAAU,KAKoC;QALpC,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD,GALpC;IAMjB,qBACE,4TAAC,uRAAuB;QACtB,WAAW,IAAA,qHAAE,EACX,kKACA;QAEF,aAAU;QACV,YAAY;QACZ,aAAa;QACZ,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/label.tsx"], "sourcesContent": ["'use client';\n\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      className={cn(\n        'flex select-none items-center gap-2 font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-50 group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50',\n        className\n      )}\n      data-slot=\"label\"\n      {...props}\n    />\n  );\n}\n\nexport { Label };\n"], "names": [], "mappings": ";;;;;AAEA;AAGA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,4TAAC,yRAAmB;QAClB,WAAW,IAAA,qHAAE,EACX,uNACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/form.tsx"], "sourcesContent": ["'use client';\n\nimport type * as LabelPrimitive from '@radix-ui/react-label';\nimport { Slot } from '@radix-ui/react-slot';\nimport * as React from 'react';\nimport {\n  Controller,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues,\n  FormProvider,\n  useFormContext,\n  useFormState,\n} from 'react-hook-form';\nimport { Label } from '@/components/ui/label';\nimport { cn } from '@/lib/utils';\n\nconst Form = FormProvider;\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n> = {\n  name: TName;\n};\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n);\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  );\n};\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext);\n  const itemContext = React.useContext(FormItemContext);\n  const { getFieldState } = useFormContext();\n  const formState = useFormState({ name: fieldContext.name });\n  const fieldState = getFieldState(fieldContext.name, formState);\n\n  if (!fieldContext) {\n    throw new Error('useFormField should be used within <FormField>');\n  }\n\n  const { id } = itemContext;\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  };\n};\n\ntype FormItemContextValue = {\n  id: string;\n};\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n);\n\nfunction FormItem({ className, ...props }: React.ComponentProps<'div'>) {\n  const id = React.useId();\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div\n        className={cn('grid gap-2', className)}\n        data-slot=\"form-item\"\n        {...props}\n      />\n    </FormItemContext.Provider>\n  );\n}\n\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const { error, formItemId } = useFormField();\n\n  return (\n    <Label\n      className={cn('data-[error=true]:text-destructive', className)}\n      data-error={!!error}\n      data-slot=\"form-label\"\n      htmlFor={formItemId}\n      {...props}\n    />\n  );\n}\n\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\n  const { error, formItemId, formDescriptionId, formMessageId } =\n    useFormField();\n\n  return (\n    <Slot\n      aria-describedby={\n        error ? `${formDescriptionId} ${formMessageId}` : `${formDescriptionId}`\n      }\n      aria-invalid={!!error}\n      data-slot=\"form-control\"\n      id={formItemId}\n      {...props}\n    />\n  );\n}\n\nfunction FormDescription({ className, ...props }: React.ComponentProps<'p'>) {\n  const { formDescriptionId } = useFormField();\n\n  return (\n    <p\n      className={cn('text-muted-foreground text-sm', className)}\n      data-slot=\"form-description\"\n      id={formDescriptionId}\n      {...props}\n    />\n  );\n}\n\nfunction FormMessage({ className, ...props }: React.ComponentProps<'p'>) {\n  const { error, formMessageId } = useFormField();\n  const body = error ? String(error?.message ?? '') : props.children;\n\n  if (!body) {\n    return null;\n  }\n\n  return (\n    <p\n      className={cn('text-destructive text-sm', className)}\n      data-slot=\"form-message\"\n      id={formMessageId}\n      {...props}\n    >\n      {body}\n    </p>\n  );\n}\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA;AACA;AACA;AASA;AACA;;;AAfA;;;;;;AAiBA,MAAM,OAAO,0QAAY;AASzB,MAAM,iCAAmB,6SAAmB,CAC1C,CAAC;AAGH,MAAM,YAAY;QAGhB,EACA,GAAG,OACkC;IACrC,qBACE,4TAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,4TAAC,wQAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAXM;AAaN,MAAM,eAAe;;IACnB,MAAM,eAAe,0SAAgB,CAAC;IACtC,MAAM,cAAc,0SAAgB,CAAC;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,IAAA,4QAAc;IACxC,MAAM,YAAY,IAAA,0QAAY,EAAC;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,AAAC,GAAK,OAAH,IAAG;QAClB,mBAAmB,AAAC,GAAK,OAAH,IAAG;QACzB,eAAe,AAAC,GAAK,OAAH,IAAG;QACrB,GAAG,UAAU;IACf;AACF;GArBM;;QAGsB,4QAAc;QACtB,0QAAY;;;AAuBhC,MAAM,gCAAkB,6SAAmB,CACzC,CAAC;AAGH,SAAS,SAAS,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;;IAChB,MAAM,KAAK,qSAAW;IAEtB,qBACE,4TAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,4TAAC;YACC,WAAW,IAAA,qHAAE,EAAC,cAAc;YAC5B,aAAU;YACT,GAAG,KAAK;;;;;;;;;;;AAIjB;IAZS;MAAA;AAcT,SAAS,UAAU,KAGgC;QAHhC,EACjB,SAAS,EACT,GAAG,OAC8C,GAHhC;;IAIjB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,4TAAC,sIAAK;QACJ,WAAW,IAAA,qHAAE,EAAC,sCAAsC;QACpD,cAAY,CAAC,CAAC;QACd,aAAU;QACV,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;IAfS;;QAIuB;;;MAJvB;AAiBT,SAAS,YAAY,KAA+C;QAA/C,EAAE,GAAG,OAA0C,GAA/C;;IACnB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAC3D;IAEF,qBACE,4TAAC,gTAAI;QACH,oBACE,QAAQ,AAAC,GAAuB,OAArB,mBAAkB,KAAiB,OAAd,iBAAkB,AAAC,GAAoB,OAAlB;QAEvD,gBAAc,CAAC,CAAC;QAChB,aAAU;QACV,IAAI;QACH,GAAG,KAAK;;;;;;AAGf;IAfS;;QAEL;;;MAFK;AAiBT,SAAS,gBAAgB,KAAkD;QAAlD,EAAE,SAAS,EAAE,GAAG,OAAkC,GAAlD;;IACvB,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,4TAAC;QACC,WAAW,IAAA,qHAAE,EAAC,iCAAiC;QAC/C,aAAU;QACV,IAAI;QACH,GAAG,KAAK;;;;;;AAGf;IAXS;;QACuB;;;MADvB;AAaT,SAAS,YAAY,KAAkD;QAAlD,EAAE,SAAS,EAAE,GAAG,OAAkC,GAAlD;;IACnB,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;QACL;IAA5B,MAAM,OAAO,QAAQ,OAAO,CAAA,iBAAA,kBAAA,4BAAA,MAAO,OAAO,cAAd,4BAAA,iBAAkB,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,4TAAC;QACC,WAAW,IAAA,qHAAE,EAAC,4BAA4B;QAC1C,aAAU;QACV,IAAI;QACH,GAAG,KAAK;kBAER;;;;;;AAGP;IAlBS;;QAC0B;;;MAD1B", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/server/schema.ts"], "sourcesContent": ["import { z } from 'zod';\n\nexport const SubFormSchema = z.object({\n  email: z.email('Please enter a valid email address'),\n});\n\nexport type TSubFormSchema = z.infer<typeof SubFormSchema>;\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,gBAAgB,qOAAC,CAAC,MAAM,CAAC;IACpC,OAAO,qOAAC,CAAC,KAAK,CAAC;AACjB", "debugId": null}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/server/subscribe.action.ts"], "sourcesContent": ["'use server';\n\nimport { Resend } from 'resend';\nimport { SubscribedTemplate } from '@/components/emails/subscribe-template';\nimport type { TSubFormSchema } from './schema';\n\n// Initialize Resend client with API key from environment variables\nconst resendClient = new Resend(process.env.RESEND_API_KEY);\n\nexport async function subscribe(formData: TSubFormSchema) {\n  try {\n    const { email } = formData;\n\n    const response = await resendClient.emails.send({\n      from: 'Rathon <<EMAIL>>',\n      to: ['<EMAIL>'],\n      subject: 'New Subscriber from Rathon Website',\n      react: SubscribedTemplate({ email }) as React.ReactElement,\n      replyTo: email,\n      tags: [{ name: 'source', value: 'website_subscribe' }],\n    });\n\n    if (response.error) {\n      return { success: false, error: response.error };\n    }\n\n    return { success: true };\n  } catch (error) {\n    return { success: false, error };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;IASsB,YAAA,WAAA,GAAA,IAAA,iXAAA,EAAA,8CAAA,sWAAA,EAAA,KAAA,GAAA,4WAAA,EAAA", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/button.tsx"], "sourcesContent": ["import { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  \"inline-flex shrink-0 cursor-pointer items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium text-sm outline-none transition-all focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\n        destructive:\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:bg-destructive/60 dark:focus-visible:ring-destructive/40',\n        outline:\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:border-input dark:bg-input/30 dark:hover:bg-input/50',\n        secondary:\n          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\n        ghost:\n          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\n        link: 'text-primary underline-offset-4 hover:underline',\n      },\n      size: {\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\n        sm: 'h-8 gap-1.5 rounded-md px-3 has-[>svg]:px-2.5',\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\n        icon: 'size-9',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'button'> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : 'button';\n\n  return (\n    <Comp\n      className={cn(buttonVariants({ variant, size, className }))}\n      data-slot=\"button\"\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAGA;;;;;AAEA,MAAM,iBAAiB,IAAA,qPAAG,EACxB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,gTAAI,GAAG;IAE9B,qBACE,4TAAC;QACC,WAAW,IAAA,qHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/input.tsx"], "sourcesContent": ["import type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Input({ className, type, ...props }: React.ComponentProps<'input'>) {\n  return (\n    <input\n      className={cn(\n        'flex h-9 w-full min-w-0 rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-xs outline-none transition-[color,box-shadow] selection:bg-primary selection:text-primary-foreground file:inline-flex file:h-7 file:border-0 file:bg-transparent file:font-medium file:text-foreground file:text-sm placeholder:text-muted-foreground disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm dark:bg-input/30',\n        'focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50',\n        'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',\n        className\n      )}\n      data-slot=\"input\"\n      type={type}\n      {...props}\n    />\n  );\n}\n\nexport { Input };\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,4TAAC;QACC,WAAW,IAAA,qHAAE,EACX,mcACA,iFACA,0GACA;QAEF,aAAU;QACV,MAAM;QACL,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/layout/footer-subscribe-form.tsx"], "sourcesContent": ["'use client';\n\nimport { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';\nimport { Loader2 } from 'lucide-react';\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { toast } from 'sonner';\nimport {\n  Form,\n  FormControl,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from '@/components/ui/form';\nimport { SubFormSchema, type TSubFormSchema } from '@/server/schema';\nimport { subscribe } from '@/server/subscribe.action';\nimport { Button } from '../ui/button';\nimport { Input } from '../ui/input';\n\nexport default function FooterSubForm() {\n  const [submitting, setSubmitting] = useState(false);\n  const [subscribed, setSubscribed] = useState(false);\n\n  const form = useForm<TSubFormSchema>({\n    resolver: zodResolver(SubFormSchema),\n    defaultValues: {\n      email: '',\n    },\n  });\n\n  const onSubmit = async (data: TSubFormSchema) => {\n    setSubmitting(true);\n\n    const createPromise = subscribe(data);\n\n    toast.promise(createPromise, {\n      loading: 'Subscribing...',\n    });\n\n    try {\n      const result = await createPromise;\n\n      if (result?.success) {\n        setSubscribed(true); // trigger confirmation message\n        form.reset();\n\n        toast.success('Subscribed successfully', {\n          description: 'You have been subscribed to Rathon.',\n        });\n      }\n    } catch {\n      toast.error('Failed to subscribe. Please try again.', {\n        description: 'There was an error subscribing to Rathon.',\n      });\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  if (subscribed) {\n    return (\n      <div className=\"flex flex-col gap-2\">\n        <p className=\"font-bold text-muted-foreground text-sm\">\n          You have been subscribed to Rathon.\n        </p>\n        <p className=\"text-muted-foreground text-sm\">\n          Thank you for subscribing!\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <Form {...form}>\n      <form\n        className=\"flex w-full max-w-sm items-center space-x-2\"\n        onSubmit={form.handleSubmit(onSubmit)}\n      >\n        <FormField\n          control={form.control}\n          name=\"email\"\n          render={({ field }) => (\n            <FormItem className=\"flex flex-col gap-5\">\n              <FormLabel className=\"sr-only font-bold\">Email</FormLabel>\n              <FormControl>\n                <Input\n                  autoComplete=\"email\"\n                  disabled={submitting}\n                  placeholder=\"<EMAIL>\"\n                  type=\"email\"\n                  {...field}\n                />\n              </FormControl>\n              <FormMessage />\n            </FormItem>\n          )}\n        />\n\n        <div className=\"flex justify-end\">\n          <Button\n            className=\"bg-primary text-primary-foreground\"\n            disabled={submitting}\n            size=\"sm\"\n            type=\"submit\"\n          >\n            {submitting ? (\n              <>\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                Subscribing...\n              </>\n            ) : (\n              'Subscribe'\n            )}\n          </Button>\n        </div>\n      </form>\n    </Form>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;;;AAlBA;;;;;;;;;;;AAoBe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,wSAAQ,EAAC;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,wSAAQ,EAAC;IAE7C,MAAM,OAAO,IAAA,qQAAO,EAAiB;QACnC,UAAU,IAAA,uRAAW,EAAC,oIAAa;QACnC,eAAe;YACb,OAAO;QACT;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,cAAc;QAEd,MAAM,gBAAgB,IAAA,8JAAS,EAAC;QAEhC,oRAAK,CAAC,OAAO,CAAC,eAAe;YAC3B,SAAS;QACX;QAEA,IAAI;YACF,MAAM,SAAS,MAAM;YAErB,IAAI,mBAAA,6BAAA,OAAQ,OAAO,EAAE;gBACnB,cAAc,OAAO,+BAA+B;gBACpD,KAAK,KAAK;gBAEV,oRAAK,CAAC,OAAO,CAAC,2BAA2B;oBACvC,aAAa;gBACf;YACF;QACF,EAAE,UAAM;YACN,oRAAK,CAAC,KAAK,CAAC,0CAA0C;gBACpD,aAAa;YACf;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,IAAI,YAAY;QACd,qBACE,4TAAC;YAAI,WAAU;;8BACb,4TAAC;oBAAE,WAAU;8BAA0C;;;;;;8BAGvD,4TAAC;oBAAE,WAAU;8BAAgC;;;;;;;;;;;;IAKnD;IAEA,qBACE,4TAAC,oIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,4TAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,4TAAC,yIAAS;oBACR,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,QAAQ;4BAAC,EAAE,KAAK,EAAE;6CAChB,4TAAC,wIAAQ;4BAAC,WAAU;;8CAClB,4TAAC,yIAAS;oCAAC,WAAU;8CAAoB;;;;;;8CACzC,4TAAC,2IAAW;8CACV,cAAA,4TAAC,sIAAK;wCACJ,cAAa;wCACb,UAAU;wCACV,aAAY;wCACZ,MAAK;wCACJ,GAAG,KAAK;;;;;;;;;;;8CAGb,4TAAC,2IAAW;;;;;;;;;;;;;;;;;8BAKlB,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC,wIAAM;wBACL,WAAU;wBACV,UAAU;wBACV,MAAK;wBACL,MAAK;kCAEJ,2BACC;;8CACE,4TAAC,mTAAO;oCAAC,WAAU;;;;;;gCAA8B;;2CAInD;;;;;;;;;;;;;;;;;;;;;;AAOd;GAnGwB;;QAIT,qQAAO;;;KAJE", "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/navigation-menu.tsx"], "sourcesContent": ["import * as NavigationMenuPrimitive from '@radix-ui/react-navigation-menu';\nimport { cva } from 'class-variance-authority';\nimport { ChevronDownIcon } from 'lucide-react';\nimport type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction NavigationMenu({\n  className,\n  children,\n  viewport = true,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Root> & {\n  viewport?: boolean;\n}) {\n  return (\n    <NavigationMenuPrimitive.Root\n      className={cn(\n        'group/navigation-menu relative flex max-w-max flex-1 items-center justify-center',\n        className\n      )}\n      data-slot=\"navigation-menu\"\n      data-viewport={viewport}\n      {...props}\n    >\n      {children}\n      {viewport && <NavigationMenuViewport />}\n    </NavigationMenuPrimitive.Root>\n  );\n}\n\nfunction NavigationMenuList({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.List>) {\n  return (\n    <NavigationMenuPrimitive.List\n      className={cn(\n        'group flex flex-1 list-none items-center justify-center gap-1',\n        className\n      )}\n      data-slot=\"navigation-menu-list\"\n      {...props}\n    />\n  );\n}\n\nfunction NavigationMenuItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Item>) {\n  return (\n    <NavigationMenuPrimitive.Item\n      className={cn('relative', className)}\n      data-slot=\"navigation-menu-item\"\n      {...props}\n    />\n  );\n}\n\nconst navigationMenuTriggerStyle = cva(\n  'group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 font-medium text-sm outline-none transition-[color,box-shadow] hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:outline-1 focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 data-[state=open]:bg-accent/50 data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:hover:bg-accent'\n);\n\nfunction NavigationMenuTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Trigger>) {\n  return (\n    <NavigationMenuPrimitive.Trigger\n      className={cn(navigationMenuTriggerStyle(), 'group', className)}\n      data-slot=\"navigation-menu-trigger\"\n      {...props}\n    >\n      {children}{' '}\n      <ChevronDownIcon\n        aria-hidden=\"true\"\n        className=\"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180\"\n      />\n    </NavigationMenuPrimitive.Trigger>\n  );\n}\n\nfunction NavigationMenuContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Content>) {\n  return (\n    <NavigationMenuPrimitive.Content\n      className={cn(\n        'data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out md:absolute md:w-auto',\n        'group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 **:data-[slot=navigation-menu-link]:focus:outline-none **:data-[slot=navigation-menu-link]:focus:ring-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in',\n        className\n      )}\n      data-slot=\"navigation-menu-content\"\n      {...props}\n    />\n  );\n}\n\nfunction NavigationMenuViewport({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Viewport>) {\n  return (\n    <div\n      className={cn(\n        'absolute top-full left-0 isolate z-50 flex justify-center'\n      )}\n    >\n      <NavigationMenuPrimitive.Viewport\n        className={cn(\n          'data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full origin-top-center overflow-hidden rounded-2xl border-0 bg-popover text-popover-foreground shadow data-[state=closed]:animate-out data-[state=open]:animate-in md:w-[var(--radix-navigation-menu-viewport-width)]',\n          className\n        )}\n        data-slot=\"navigation-menu-viewport\"\n        {...props}\n      />\n    </div>\n  );\n}\n\nfunction NavigationMenuLink({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Link>) {\n  return (\n    <NavigationMenuPrimitive.Link\n      className={cn(\n        \"flex flex-col gap-1 rounded-sm p-2 text-sm outline-none transition-all hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:outline-1 focus-visible:ring-[3px] focus-visible:ring-ring/50 data-[active=true]:bg-accent/50 data-[active=true]:text-accent-foreground data-[active=true]:focus:bg-accent data-[active=true]:hover:bg-accent [&_svg:not([class*='size-'])]:size-4 [&_svg:not([class*='text-'])]:text-muted-foreground\",\n        className\n      )}\n      data-slot=\"navigation-menu-link\"\n      {...props}\n    />\n  );\n}\n\nfunction NavigationMenuIndicator({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Indicator>) {\n  return (\n    <NavigationMenuPrimitive.Indicator\n      className={cn(\n        'data-[state=hidden]:fade-out data-[state=visible]:fade-in top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden data-[state=hidden]:animate-out data-[state=visible]:animate-in',\n        className\n      )}\n      data-slot=\"navigation-menu-indicator\"\n      {...props}\n    >\n      <div className=\"relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md\" />\n    </NavigationMenuPrimitive.Indicator>\n  );\n}\n\nexport {\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n  navigationMenuTriggerStyle,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAGA;;;;;;AAEA,SAAS,eAAe,KAOvB;QAPuB,EACtB,SAAS,EACT,QAAQ,EACR,WAAW,IAAI,EACf,GAAG,OAGJ,GAPuB;IAQtB,qBACE,4TAAC,gSAA4B;QAC3B,WAAW,IAAA,qHAAE,EACX,oFACA;QAEF,aAAU;QACV,iBAAe;QACd,GAAG,KAAK;;YAER;YACA,0BAAY,4TAAC;;;;;;;;;;;AAGpB;KAtBS;AAwBT,SAAS,mBAAmB,KAGgC;QAHhC,EAC1B,SAAS,EACT,GAAG,OACuD,GAHhC;IAI1B,qBACE,4TAAC,gSAA4B;QAC3B,WAAW,IAAA,qHAAE,EACX,iEACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,mBAAmB,KAGgC;QAHhC,EAC1B,SAAS,EACT,GAAG,OACuD,GAHhC;IAI1B,qBACE,4TAAC,gSAA4B;QAC3B,WAAW,IAAA,qHAAE,EAAC,YAAY;QAC1B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,MAAM,6BAA6B,IAAA,qPAAG,EACpC;AAGF,SAAS,sBAAsB,KAIgC;QAJhC,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D,GAJhC;IAK7B,qBACE,4TAAC,mSAA+B;QAC9B,WAAW,IAAA,qHAAE,EAAC,8BAA8B,SAAS;QACrD,aAAU;QACT,GAAG,KAAK;;YAER;YAAU;0BACX,4TAAC,kUAAe;gBACd,eAAY;gBACZ,WAAU;;;;;;;;;;;;AAIlB;MAlBS;AAoBT,SAAS,sBAAsB,KAGgC;QAHhC,EAC7B,SAAS,EACT,GAAG,OAC0D,GAHhC;IAI7B,qBACE,4TAAC,mSAA+B;QAC9B,WAAW,IAAA,qHAAE,EACX,oWACA,6hCACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAfS;AAiBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,4TAAC;QACC,WAAW,IAAA,qHAAE,EACX;kBAGF,cAAA,4TAAC,oSAAgC;YAC/B,WAAW,IAAA,qHAAE,EACX,yVACA;YAEF,aAAU;YACT,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,mBAAmB,KAGgC;QAHhC,EAC1B,SAAS,EACT,GAAG,OACuD,GAHhC;IAI1B,qBACE,4TAAC,gSAA4B;QAC3B,WAAW,IAAA,qHAAE,EACX,ydACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,wBAAwB,KAGgC;QAHhC,EAC/B,SAAS,EACT,GAAG,OAC4D,GAHhC;IAI/B,qBACE,4TAAC,qSAAiC;QAChC,WAAW,IAAA,qHAAE,EACX,gMACA;QAEF,aAAU;QACT,GAAG,KAAK;kBAET,cAAA,4TAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;MAhBS", "debugId": null}}, {"offset": {"line": 824, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/config/docs.ts"], "sourcesContent": ["import {\n  ChartNoAxesCombinedIcon,\n  CircleCheckIcon,\n  DatabaseZapIcon,\n  HouseIcon,\n  ImageIcon,\n  MessageCircleIcon,\n  NotebookPenIcon,\n  PenLineIcon,\n  UsersIcon,\n} from 'lucide-react';\nimport type { TFeature, TFooterLink, TNavItem } from '@/types';\n\nexport const navItems: TNavItem[] = [\n  {\n    href: '/pricing',\n    label: 'Pricing',\n    icon: HouseIcon,\n  },\n  {\n    href: '/about',\n    label: 'About',\n    icon: NotebookPenIcon,\n  },\n  {\n    href: '/docs',\n    label: 'Docs',\n    icon: UsersIcon,\n  },\n  {\n    href: '/privacy',\n    label: 'Privacy',\n    icon: ImageIcon,\n  },\n];\n\nexport const footerLinks: TFooterLink[] = [\n  {\n    title: 'Newsroom',\n    links: [\n      { name: 'Latest News', href: '/', external: false },\n      { name: 'Top Stories', href: '/', external: false },\n      { name: \"Editor's Picks\", href: '/', external: false },\n    ],\n  },\n  {\n    title: 'Company',\n    links: [\n      { name: 'About Us', href: '/', external: false },\n      { name: 'Careers', href: '/', external: false },\n      { name: 'Press', href: '/', external: false },\n      { name: 'Contact', href: '/', external: false },\n    ],\n  },\n  {\n    title: 'For Business',\n    links: [\n      { name: 'Advertise with Us', href: '/', external: false },\n      { name: 'Media Kit', href: '/', external: false },\n      { name: 'Partner with Us', href: '/', external: false },\n    ],\n  },\n  {\n    title: 'More',\n    links: [\n      { name: 'Newsletter', href: '/', external: false },\n      { name: 'Mobile App', href: '/', external: false },\n      { name: 'RSS Feeds', href: '/', external: false },\n      { name: 'Help Center', href: '/', external: false },\n    ],\n  },\n  {\n    title: 'Terms & Policies',\n    links: [\n      { name: 'Terms of Use', href: '/', external: false },\n      { name: 'Privacy Policy', href: '/', external: false },\n      { name: 'Cookie Policy', href: '/', external: false },\n      { name: 'Editorial Policy', href: '/', external: false },\n    ],\n  },\n  {\n    title: 'Safety',\n    links: [\n      { name: 'Fact-Checking', href: '/', external: false },\n      { name: 'Corrections', href: '/', external: false },\n      { name: 'Trust & Transparency', href: '/', external: false },\n    ],\n  },\n  {\n    title: 'Follow Us',\n    links: [\n      { name: 'Facebook', href: '/', external: true },\n      { name: 'Twitter', href: '/', external: true },\n      { name: 'Instagram', href: '/', external: true },\n      { name: 'YouTube', href: '/', external: true },\n    ],\n  },\n  {\n    title: 'Sections',\n    links: [\n      { name: 'Politics', href: '/', external: false },\n      { name: 'Business', href: '/', external: false },\n      { name: 'Technology', href: '/', external: false },\n      { name: 'Health', href: '/', external: false },\n    ],\n  },\n  {\n    title: 'Resources',\n    links: [\n      { name: 'Media Resources', href: '/', external: false },\n      { name: 'Author Guidelines', href: '/', external: false },\n      { name: 'News Archive', href: '/', external: false },\n    ],\n  },\n  {\n    title: 'Community',\n    links: [\n      { name: 'Events', href: '/', external: false },\n      { name: 'Reader Stories', href: '/', external: false },\n      { name: 'Submit News', href: '/', external: false },\n    ],\n  },\n];\n\nexport const features: TFeature[] = [\n  {\n    icon: MessageCircleIcon,\n    title: 'chat',\n    description: 'Chat with anyone in team.',\n  },\n  {\n    icon: PenLineIcon,\n    title: 'writing',\n    description: 'Notion like editor for writing.',\n  },\n  {\n    icon: CircleCheckIcon,\n    title: 'tasks',\n    description: 'Automated task tracking.',\n  },\n  {\n    icon: UsersIcon,\n    title: 'teams',\n    description: 'Collaborate with your team.',\n  },\n  {\n    icon: DatabaseZapIcon,\n    title: 'storage',\n    description: 'Unlimited storage for your files.',\n  },\n  {\n    icon: ChartNoAxesCombinedIcon,\n    title: 'analytics',\n    description: 'Easy to track your progress.',\n  },\n];\n\nexport const featuresCompare = [\n  {\n    feature: \"Doesn't train on your data\",\n    Highlight: true,\n    ChatGPT: false,\n    Claude: false,\n    Raycast: true,\n    Notion: true,\n  },\n  {\n    feature: 'Works across your entire computer',\n    Highlight: true,\n    ChatGPT: false,\n    Claude: false,\n    Raycast: true,\n    Notion: false,\n  },\n  {\n    feature: 'Always one click away',\n    Highlight: true,\n    ChatGPT: false,\n    Claude: false,\n    Raycast: true,\n    Notion: false,\n  },\n  {\n    feature: 'Custom actions and automations',\n    Highlight: true,\n    ChatGPT: false,\n    Claude: false,\n    Raycast: true,\n    Notion: false,\n  },\n  {\n    feature: \"Understands anything you're looking at\",\n    Highlight: true,\n    ChatGPT: false,\n    Claude: false,\n    Raycast: false,\n    Notion: false,\n  },\n  {\n    feature: 'Integrated audio transcription',\n    Highlight: true,\n    ChatGPT: false,\n    Claude: false,\n    Raycast: false,\n    Notion: false,\n  },\n  {\n    feature: 'Built for both Mac & Windows',\n    Highlight: true,\n    ChatGPT: false,\n    Claude: false,\n    Raycast: false,\n    Notion: false,\n  },\n];\n\nexport const reviews = [\n  {\n    name: 'Eric Glyman',\n    username: 'Co-Founder at Ramp',\n    body: \"I've never seen anything like this before. It's amazing. I love it.\",\n    img: 'https://avatar.vercel.sh/jack',\n  },\n  {\n    name: 'Eric James',\n    username: 'Co-Founder at Ramp',\n    body: \"I don't know what to say. I'm speechless. This is amazing.\",\n    img: 'https://avatar.vercel.sh/jill',\n  },\n  {\n    name: 'Eric Kagabo',\n    username: 'Co-Founder at Ramp',\n    body: \"I'm at a loss for words. This is amazing. I love it.\",\n    img: 'https://avatar.vercel.sh/john',\n  },\n  {\n    name: 'Eric Mugisha',\n    username: 'Co-Founder at Ramp',\n    body: \"I'm at a loss for words. This is amazing. I love it.\",\n    img: 'https://avatar.vercel.sh/jane',\n  },\n  {\n    name: 'Eric David',\n    username: 'Co-Founder at Ramp',\n    body: \"I'm at a loss for words. This is amazing. I love it.\",\n    img: 'https://avatar.vercel.sh/jenny',\n  },\n  {\n    name: 'Eric Tony',\n    username: 'Co-Founder at Ramp',\n    body: \"I'm at a loss for words. This is amazing. I love it.\",\n    img: 'https://avatar.vercel.sh/james',\n  },\n];\n\nexport const links = [\n  {\n    group: 'Company',\n    items: [\n      {\n        title: 'About',\n        href: '/about',\n        external: false,\n      },\n      {\n        title: 'Blog',\n        href: '/blogs',\n        external: false,\n      },\n      {\n        title: 'Pricing',\n        href: '/#pricing',\n        external: false,\n      },\n      {\n        title: 'Book a Call',\n        href: '/book',\n        external: false,\n      },\n    ],\n  },\n  {\n    group: 'Resources',\n    items: [\n      {\n        title: 'Careers',\n        href: '/contact',\n        external: false,\n      },\n\n      {\n        title: 'Support',\n        href: '/book',\n        external: false,\n      },\n      {\n        title: 'Sitemap',\n        href: '/sitemap.xml',\n        external: false,\n      },\n      {\n        title: 'llm.txt',\n        href: '/llm.txt',\n        external: false,\n      },\n    ],\n  },\n  {\n    group: 'Legal',\n    items: [\n      {\n        title: 'Privacy',\n        href: '/privacy',\n        external: false,\n      },\n      {\n        title: 'Terms',\n        href: '/terms',\n        external: false,\n      },\n    ],\n  },\n];\n\nexport const plans = [\n  {\n    id: 'free',\n    name: 'Free',\n    price: {\n      monthly: 0,\n      yearly: 0,\n    },\n    description:\n      'The perfect starting place for your web app or personal project.',\n    features: [\n      '50 API calls / month',\n      '60 second checks',\n      'Single-user account',\n      '5 monitors',\n      'Basic email support',\n    ],\n    cta: 'Get started for free',\n  },\n  {\n    id: 'pro',\n    name: 'Pro',\n    price: {\n      monthly: 90,\n      yearly: 75,\n    },\n    description: 'Everything you need to build and scale your business.',\n    features: [\n      'Unlimited API calls',\n      '30 second checks',\n      'Multi-user account',\n      '10 monitors',\n      'Priority email support',\n    ],\n    cta: 'Subscribe to Pro',\n    popular: true,\n  },\n  {\n    id: 'enterprise',\n    name: 'Enterprise',\n    price: {\n      monthly: 'Get in touch for pricing',\n      yearly: 'Get in touch for pricing',\n    },\n    description: 'Critical security, performance, observability and support.',\n    features: [\n      'You can DDOS our API.',\n      'Nano-second checks.',\n      'Invite your extended family.',\n      'Unlimited monitors.',\n      \"We'll sit on your desk.\",\n    ],\n    cta: 'Contact us',\n  },\n];\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAaO,MAAM,WAAuB;IAClC;QACE,MAAM;QACN,OAAO;QACP,MAAM,4SAAS;IACjB;IACA;QACE,MAAM;QACN,OAAO;QACP,MAAM,kUAAe;IACvB;IACA;QACE,MAAM;QACN,OAAO;QACP,MAAM,4SAAS;IACjB;IACA;QACE,MAAM;QACN,OAAO;QACP,MAAM,4SAAS;IACjB;CACD;AAEM,MAAM,cAA6B;IACxC;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;YAClD;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;YAClD;gBAAE,MAAM;gBAAkB,MAAM;gBAAK,UAAU;YAAM;SACtD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAK,UAAU;YAAM;YAC/C;gBAAE,MAAM;gBAAW,MAAM;gBAAK,UAAU;YAAM;YAC9C;gBAAE,MAAM;gBAAS,MAAM;gBAAK,UAAU;YAAM;YAC5C;gBAAE,MAAM;gBAAW,MAAM;gBAAK,UAAU;YAAM;SAC/C;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAqB,MAAM;gBAAK,UAAU;YAAM;YACxD;gBAAE,MAAM;gBAAa,MAAM;gBAAK,UAAU;YAAM;YAChD;gBAAE,MAAM;gBAAmB,MAAM;gBAAK,UAAU;YAAM;SACvD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAc,MAAM;gBAAK,UAAU;YAAM;YACjD;gBAAE,MAAM;gBAAc,MAAM;gBAAK,UAAU;YAAM;YACjD;gBAAE,MAAM;gBAAa,MAAM;gBAAK,UAAU;YAAM;YAChD;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;SACnD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAgB,MAAM;gBAAK,UAAU;YAAM;YACnD;gBAAE,MAAM;gBAAkB,MAAM;gBAAK,UAAU;YAAM;YACrD;gBAAE,MAAM;gBAAiB,MAAM;gBAAK,UAAU;YAAM;YACpD;gBAAE,MAAM;gBAAoB,MAAM;gBAAK,UAAU;YAAM;SACxD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAiB,MAAM;gBAAK,UAAU;YAAM;YACpD;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;YAClD;gBAAE,MAAM;gBAAwB,MAAM;gBAAK,UAAU;YAAM;SAC5D;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAK,UAAU;YAAK;YAC9C;gBAAE,MAAM;gBAAW,MAAM;gBAAK,UAAU;YAAK;YAC7C;gBAAE,MAAM;gBAAa,MAAM;gBAAK,UAAU;YAAK;YAC/C;gBAAE,MAAM;gBAAW,MAAM;gBAAK,UAAU;YAAK;SAC9C;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAK,UAAU;YAAM;YAC/C;gBAAE,MAAM;gBAAY,MAAM;gBAAK,UAAU;YAAM;YAC/C;gBAAE,MAAM;gBAAc,MAAM;gBAAK,UAAU;YAAM;YACjD;gBAAE,MAAM;gBAAU,MAAM;gBAAK,UAAU;YAAM;SAC9C;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAmB,MAAM;gBAAK,UAAU;YAAM;YACtD;gBAAE,MAAM;gBAAqB,MAAM;gBAAK,UAAU;YAAM;YACxD;gBAAE,MAAM;gBAAgB,MAAM;gBAAK,UAAU;YAAM;SACpD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAU,MAAM;gBAAK,UAAU;YAAM;YAC7C;gBAAE,MAAM;gBAAkB,MAAM;gBAAK,UAAU;YAAM;YACrD;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;SACnD;IACH;CACD;AAEM,MAAM,WAAuB;IAClC;QACE,MAAM,wUAAiB;QACvB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,sTAAW;QACjB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,kUAAe;QACrB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,4SAAS;QACf,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,kUAAe;QACrB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,kWAAuB;QAC7B,OAAO;QACP,aAAa;IACf;CACD;AAEM,MAAM,kBAAkB;IAC7B;QACE,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;CACD;AAEM,MAAM,UAAU;IACrB;QACE,MAAM;QACN,UAAU;QACV,MAAM;QACN,KAAK;IACP;IACA;QACE,MAAM;QACN,UAAU;QACV,MAAM;QACN,KAAK;IACP;IACA;QACE,MAAM;QACN,UAAU;QACV,MAAM;QACN,KAAK;IACP;IACA;QACE,MAAM;QACN,UAAU;QACV,MAAM;QACN,KAAK;IACP;IACA;QACE,MAAM;QACN,UAAU;QACV,MAAM;QACN,KAAK;IACP;IACA;QACE,MAAM;QACN,UAAU;QACV,MAAM;QACN,KAAK;IACP;CACD;AAEM,MAAM,QAAQ;IACnB;QACE,OAAO;QACP,OAAO;YACL;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;YAEA;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;IACH;CACD;AAEM,MAAM,QAAQ;IACnB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;YACL,SAAS;YACT,QAAQ;QACV;QACA,aACE;QACF,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;IACP;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;YACL,SAAS;YACT,QAAQ;QACV;QACA,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;YACL,SAAS;YACT,QAAQ;QACV;QACA,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;IACP;CACD", "debugId": null}}, {"offset": {"line": 1355, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/layout/main-nav.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Button } from '@/components/ui/button';\nimport {\n  NavigationMenu,\n  NavigationMenuContent,\n  NavigationMenuItem,\n  NavigationMenuLink,\n  NavigationMenuList,\n  NavigationMenuTrigger,\n} from '@/components/ui/navigation-menu';\nimport { features, navItems } from '@/config/docs';\nimport { cn } from '@/lib/utils';\n\nexport function MainNav({ className, ...props }: React.ComponentProps<'nav'>) {\n  const pathname = usePathname();\n\n  return (\n    <nav className={cn('items-center gap-0.5', className)} {...props}>\n      <NavigationMenu>\n        <NavigationMenuList>\n          <NavigationMenuItem className=\"rounded-2xl\">\n            <NavigationMenuTrigger\n              aria-label=\"Open blog links\"\n              className=\"flex h-7 cursor-pointer items-center rounded-full bg-transparent px-3 py-4 font-normal text-md text-primary/80 transition-all duration-300 hover:bg-accent hover:text-primary dark:hover:bg-accent/50\"\n            >\n              Features\n            </NavigationMenuTrigger>\n            <NavigationMenuContent className=\"bg-muted/5\">\n              <div className=\"grid w-[500px] p-4 lg:w-[600px]\">\n                <p className=\"font-medium text-muted-foreground capitalize tracking-tighter\">\n                  Features\n                </p>\n                <div className=\"grid grid-cols-2 gap-6 py-6\">\n                  {features.map((feature) => (\n                    <NavigationMenuLink\n                      asChild\n                      className=\"group rounded-xl p-0 hover:bg-transparent\"\n                      key={feature.title}\n                    >\n                      <Link href=\"/\">\n                        <div className=\"flex items-center gap-4\">\n                          <div className=\"rounded-lg bg-muted p-3 transition-all duration-300 group-hover:bg-brand-500 dark:group-hover:bg-brand-500\">\n                            <feature.icon className=\"block size-5 transition-all duration-300 group-hover:text-white dark:group-hover:text-black\" />\n                          </div>\n\n                          <div className=\"flex flex-col gap-1\">\n                            <div className=\"font-medium text-md capitalize leading-none\">\n                              {feature.title}\n                            </div>\n                            <p className=\"text-muted-foreground text-sm\">\n                              {feature.description}\n                            </p>\n                          </div>\n                        </div>\n                      </Link>\n                    </NavigationMenuLink>\n                  ))}\n                </div>\n              </div>\n            </NavigationMenuContent>\n          </NavigationMenuItem>\n        </NavigationMenuList>\n      </NavigationMenu>\n      {navItems.map((item) => (\n        <Button\n          asChild\n          className=\"rounded-full bg-transparent hover:bg-transparent dark:hover:bg-transparent\"\n          key={item.href}\n          size=\"sm\"\n          variant=\"ghost\"\n        >\n          <Link\n            className={cn(\n              'font-normal text-md text-primary/80 transition-all duration-300 hover:text-primary',\n              pathname === item.href && 'text-primary'\n            )}\n            href={item.href}\n          >\n            {item.label}\n          </Link>\n        </Button>\n      ))}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAQA;AACA;;;AAdA;;;;;;;AAgBO,SAAS,QAAQ,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;;IACtB,MAAM,WAAW,IAAA,mRAAW;IAE5B,qBACE,4TAAC;QAAI,WAAW,IAAA,qHAAE,EAAC,wBAAwB;QAAa,GAAG,KAAK;;0BAC9D,4TAAC,4JAAc;0BACb,cAAA,4TAAC,gKAAkB;8BACjB,cAAA,4TAAC,gKAAkB;wBAAC,WAAU;;0CAC5B,4TAAC,mKAAqB;gCACpB,cAAW;gCACX,WAAU;0CACX;;;;;;0CAGD,4TAAC,mKAAqB;gCAAC,WAAU;0CAC/B,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAE,WAAU;sDAAgE;;;;;;sDAG7E,4TAAC;4CAAI,WAAU;sDACZ,6HAAQ,CAAC,GAAG,CAAC,CAAC,wBACb,4TAAC,gKAAkB;oDACjB,OAAO;oDACP,WAAU;8DAGV,cAAA,4TAAC,ySAAI;wDAAC,MAAK;kEACT,cAAA,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;oEAAI,WAAU;8EACb,cAAA,4TAAC,QAAQ,IAAI;wEAAC,WAAU;;;;;;;;;;;8EAG1B,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;4EAAI,WAAU;sFACZ,QAAQ,KAAK;;;;;;sFAEhB,4TAAC;4EAAE,WAAU;sFACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;mDAbvB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YA0BjC,6HAAQ,CAAC,GAAG,CAAC,CAAC,qBACb,4TAAC,wIAAM;oBACL,OAAO;oBACP,WAAU;oBAEV,MAAK;oBACL,SAAQ;8BAER,cAAA,4TAAC,ySAAI;wBACH,WAAW,IAAA,qHAAE,EACX,sFACA,aAAa,KAAK,IAAI,IAAI;wBAE5B,MAAM,KAAK,IAAI;kCAEd,KAAK,KAAK;;;;;;mBAXR,KAAK,IAAI;;;;;;;;;;;AAiBxB;GAvEgB;;QACG,mRAAW;;;KADd", "debugId": null}}, {"offset": {"line": 1548, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/layout/site-header.tsx"], "sourcesContent": ["'use client';\nimport { ArmchairIcon } from 'lucide-react';\nimport Link from 'next/link';\nimport { useEffect, useState } from 'react';\nimport { cn } from '@/lib/utils';\nimport { Button } from '../ui/button';\nimport { MainNav } from './main-nav';\n\nexport default function SiteHeader() {\n  const [showBg, setShowBg] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setShowBg(window.scrollY > 60);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  return (\n    <header className=\"fixed top-4 right-0 left-0 z-50 flex w-full translate-y-0 justify-center opacity-100 transition-all duration-500 ease-out\">\n      <nav\n        className={cn(\n          'relative mx-2 flex h-[60px] w-full max-w-6xl select-none items-center justify-between rounded-full bg-transparent px-1.5 py-2 transition-all duration-200 ease-in-out sm:mx-4 xl:grid xl:grid-cols-3 xl:gap-8',\n          showBg &&\n            'bg-background/40 backdrop-blur-lg supports-backdrop-blur:bg-background/90'\n        )}\n      >\n        <div className=\"flex items-center gap-4 xl:justify-start\">\n          <div className=\"flex translate-y-0 items-center gap-1.5 px-3 opacity-100 transition-all delay-75 duration-500 ease-out\">\n            <Link className=\"flex items-center gap-1.5\" href=\"/\">\n              <ArmchairIcon className=\"block size-6 text-brand-600\" />\n              <span className=\"pb-[1.5px] font-medium text-lg\">\n                Better Flow\n              </span>\n            </Link>\n          </div>\n        </div>\n        <MainNav className=\"hidden lg:flex\" />\n        <div className=\"flex items-center justify-end gap-2 xl:justify-end\">\n          <Button className=\"rounded-full bg-brand-500 hover:bg-brand-400\">\n            Book Demo\n          </Button>\n        </div>\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;;;AANA;;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,wSAAQ,EAAC;IAErC,IAAA,ySAAS;gCAAC;YACR,MAAM;qDAAe;oBACnB,UAAU,OAAO,OAAO,GAAG;gBAC7B;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IACL,qBACE,4TAAC;QAAO,WAAU;kBAChB,cAAA,4TAAC;YACC,WAAW,IAAA,qHAAE,EACX,iNACA,UACE;;8BAGJ,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC,ySAAI;4BAAC,WAAU;4BAA4B,MAAK;;8CAC/C,4TAAC,qTAAY;oCAAC,WAAU;;;;;;8CACxB,4TAAC;oCAAK,WAAU;8CAAiC;;;;;;;;;;;;;;;;;;;;;;8BAMvD,4TAAC,kJAAO;oBAAC,WAAU;;;;;;8BACnB,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC,wIAAM;wBAAC,WAAU;kCAA+C;;;;;;;;;;;;;;;;;;;;;;AAO3E;GAvCwB;KAAA", "debugId": null}}]}