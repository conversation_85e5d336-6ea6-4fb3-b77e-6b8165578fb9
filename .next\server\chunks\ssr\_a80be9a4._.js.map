{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { Check, X } from 'lucide-react';\nimport { featuresCompare } from '@/config/docs';\nimport { cn } from '@/lib/utils';\n\nconst columns = ['Highlight', 'ChatGPT', 'Claude', 'Raycast', 'Notion'];\n\nexport default function FeatureTableLg() {\n  return (\n    <div className=\"no-scrollbar hidden w-full overflow-x-auto md:block\">\n      <table\n        className=\"w-full min-w-[700px] border-collapse border-spacing-0\"\n        style={{\n          borderSpacing: '0px',\n        }}\n      >\n        <thead>\n          <tr>\n            <th\n              className=\"sticky top-0 left-0 z-40 w-[200px] min-w-[200px] bg-[#090909] text-left font-normal text-sm md:w-[260px] md:min-w-[260px] md:text-base\"\n              scope=\"col\"\n            >\n              <div className=\"flex flex-row items-center gap-3 rounded-t-xl py-2\">\n                <span className=\"font-medium font-sans text-lg text-white\">\n                  Features\n                </span>\n              </div>\n            </th>\n            {columns.map((col, i) => (\n              <th\n                className=\"sticky top-0 left-[200px] z-30 w-[120px] min-w-[120px] bg-[#090909] px-0 text-center font-normal text-sm md:left-auto md:text-base\"\n                key={col}\n              >\n                <div\n                  className={cn(\n                    'flex flex-col items-center font-medium font-sans',\n                    i === 0\n                      ? 'rounded-t-xl py-2 text-primary'\n                      : 'p-4 text-muted-foreground'\n                  )}\n                >\n                  <div className=\"flex items-center gap-1.5\">\n                    <a className=\"flex items-center gap-1.5\" href=\"/\">\n                      <span className=\"pb-[1.5px] font-medium text-lg\">\n                        {col}\n                      </span>\n                    </a>\n                  </div>\n                </div>\n              </th>\n            ))}\n          </tr>\n        </thead>\n        <tbody>\n          {featuresCompare.map((row, i) => (\n            <tr className=\"border-0.5 border-muted/60 border-b\" key={i}>\n              <td className=\"sticky left-0 z-30 w-[200px] min-w-[200px] bg-[#090909] py-3 pr-2 align-middle md:w-[260px] md:min-w-[260px] md:pr-8\">\n                <span className=\"text-nowrap font-medium text-base text-muted-foreground leading-tight\">\n                  {row.feature}\n                </span>\n              </td>\n              {columns.map((col, i) => (\n                <td\n                  className={cn(\n                    'w-[120px] min-w-[120px] p-4 text-center align-middle',\n                    i === 0 && 'sticky left-[200px] z-20 bg-[#090909] md:static'\n                  )}\n                  key={col}\n                >\n                  <div\n                    className={cn(\n                      'mx-auto flex size-6 items-center justify-center rounded-lg text-black md:size-7',\n                      row[col as keyof typeof row]\n                        ? 'bg-brand-400'\n                        : 'bg-brand-100'\n                    )}\n                  >\n                    {row[col as keyof typeof row] ? (\n                      <Check className=\"mx-auto size-4 text-black\" />\n                    ) : (\n                      <X className=\"mx-auto size-4 text-brand-600\" />\n                    )}\n                  </div>\n                </td>\n              ))}\n            </tr>\n          ))}\n        </tbody>\n      </table>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AACA;AACA;AAJA;;;;;AAMA,MAAM,UAAU;IAAC;IAAa;IAAW;IAAU;IAAW;CAAS;AAExD,SAAS;IACtB,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YACC,WAAU;YACV,OAAO;gBACL,eAAe;YACjB;;8BAEA,6WAAC;8BACC,cAAA,6WAAC;;0CACC,6WAAC;gCACC,WAAU;gCACV,OAAM;0CAEN,cAAA,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;;;;;4BAK9D,QAAQ,GAAG,CAAC,CAAC,KAAK,kBACjB,6WAAC;oCACC,WAAU;8CAGV,cAAA,6WAAC;wCACC,WAAW,IAAA,kHAAE,EACX,oDACA,MAAM,IACF,mCACA;kDAGN,cAAA,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDAAE,WAAU;gDAA4B,MAAK;0DAC5C,cAAA,6WAAC;oDAAK,WAAU;8DACb;;;;;;;;;;;;;;;;;;;;;mCAbJ;;;;;;;;;;;;;;;;8BAsBb,6WAAC;8BACE,iIAAe,CAAC,GAAG,CAAC,CAAC,KAAK,kBACzB,6WAAC;4BAAG,WAAU;;8CACZ,6WAAC;oCAAG,WAAU;8CACZ,cAAA,6WAAC;wCAAK,WAAU;kDACb,IAAI,OAAO;;;;;;;;;;;gCAGf,QAAQ,GAAG,CAAC,CAAC,KAAK,kBACjB,6WAAC;wCACC,WAAW,IAAA,kHAAE,EACX,wDACA,MAAM,KAAK;kDAIb,cAAA,6WAAC;4CACC,WAAW,IAAA,kHAAE,EACX,mFACA,GAAG,CAAC,IAAwB,GACxB,iBACA;sDAGL,GAAG,CAAC,IAAwB,iBAC3B,6WAAC,iSAAK;gDAAC,WAAU;;;;;qEAEjB,6WAAC,qRAAC;gDAAC,WAAU;;;;;;;;;;;uCAbZ;;;;;;2BAZ8C;;;;;;;;;;;;;;;;;;;;;AAoCrE", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["/** biome-ignore-all lint/suspicious/noArrayIndexKey: <explanation> */\n'use client';\n\nimport { Check, X } from 'lucide-react';\nimport { featuresCompare } from '@/config/docs';\nimport { cn } from '@/lib/utils';\n\nconst tools = ['Highlight', 'ChatGPT', 'Claude', 'Raycast', 'Notion'];\n\nexport default function FeatureTableSm() {\n  return (\n    <div className=\"no-scrollbar block w-full overflow-x-auto md:hidden\">\n      <table\n        className=\"w-max min-w-[700px] border-collapse border-spacing-0\"\n        style={{\n          borderSpacing: '0px',\n        }}\n      >\n        <thead>\n          <tr>\n            <th className=\"sticky top-0 left-0 z-50 w-[120px] min-w-[120px] bg-[#090909] text-left\" />\n\n            {featuresCompare.map((f, i) => (\n              <th\n                className=\"sticky top-0 z-40 w-[120px] min-w-[120px] bg-[#090909] text-center font-normal text-muted-foreground text-sm\"\n                key={i}\n              >\n                <span className=\"block px-3 py-2 text-base leading-tight\">\n                  {f.feature}\n                </span>\n              </th>\n            ))}\n          </tr>\n        </thead>\n        <tbody>\n          {tools.map((tool, i) => (\n            <tr className=\"border-0.5 border-muted/60 border-b\" key={i}>\n              <td className=\"sticky left-0 z-40 w-[120px] min-w-[120px] bg-[#090909] px-3 py-3 pr-4 align-middle\">\n                <span className=\"text-nowrap font-medium font-sans text-base text-primary leading-tight\">\n                  {tool}\n                </span>\n              </td>\n              {featuresCompare.map((f, index) => (\n                <td\n                  className={cn(\n                    'w-[120px] min-w-[120px] p-4 text-center align-middle'\n                  )}\n                  key={index}\n                >\n                  <div\n                    className={cn(\n                      'mx-auto flex size-6 items-center justify-center rounded-lg text-black md:size-7',\n                      f[tool as keyof typeof f]\n                        ? 'bg-brand-400'\n                        : 'bg-brand-100'\n                    )}\n                  >\n                    {f[tool as keyof typeof f] ? (\n                      <Check className=\"mx-auto size-4 text-black\" />\n                    ) : (\n                      <X className=\"mx-auto size-4 text-brand-600\" />\n                    )}\n                  </div>\n                </td>\n              ))}\n            </tr>\n          ))}\n        </tbody>\n      </table>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,oEAAoE;;;;;AAGpE;AAAA;AACA;AACA;AAJA;;;;;AAMA,MAAM,QAAQ;IAAC;IAAa;IAAW;IAAU;IAAW;CAAS;AAEtD,SAAS;IACtB,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YACC,WAAU;YACV,OAAO;gBACL,eAAe;YACjB;;8BAEA,6WAAC;8BACC,cAAA,6WAAC;;0CACC,6WAAC;gCAAG,WAAU;;;;;;4BAEb,iIAAe,CAAC,GAAG,CAAC,CAAC,GAAG,kBACvB,6WAAC;oCACC,WAAU;8CAGV,cAAA,6WAAC;wCAAK,WAAU;kDACb,EAAE,OAAO;;;;;;mCAHP;;;;;;;;;;;;;;;;8BASb,6WAAC;8BACE,MAAM,GAAG,CAAC,CAAC,MAAM,kBAChB,6WAAC;4BAAG,WAAU;;8CACZ,6WAAC;oCAAG,WAAU;8CACZ,cAAA,6WAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;gCAGJ,iIAAe,CAAC,GAAG,CAAC,CAAC,GAAG,sBACvB,6WAAC;wCACC,WAAW,IAAA,kHAAE,EACX;kDAIF,cAAA,6WAAC;4CACC,WAAW,IAAA,kHAAE,EACX,mFACA,CAAC,CAAC,KAAuB,GACrB,iBACA;sDAGL,CAAC,CAAC,KAAuB,iBACxB,6WAAC,iSAAK;gDAAC,WAAU;;;;;qEAEjB,6WAAC,qRAAC;gDAAC,WAAU;;;;;;;;;;;uCAbZ;;;;;;2BAX8C;;;;;;;;;;;;;;;;;;;;;AAmCrE", "debugId": null}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/kibo-ui/image-zoom/index.tsx"], "sourcesContent": ["'use client';\n\nimport Zoom, {\n  type ControlledProps,\n  type UncontrolledProps,\n} from 'react-medium-image-zoom';\nimport { cn } from '@/lib/utils';\n\nexport type ImageZoomProps = UncontrolledProps & {\n  isZoomed?: ControlledProps['isZoomed'];\n  onZoomChange?: ControlledProps['onZoomChange'];\n  className?: string;\n  backdropClassName?: string;\n};\n\nexport const ImageZoom = ({\n  className,\n  backdropClassName,\n  ...props\n}: ImageZoomProps) => (\n  <div\n    className={cn(\n      'relative',\n      '[&_[data-rmiz-ghost]]:pointer-events-none [&_[data-rmiz-ghost]]:absolute',\n      '[&_[data-rmiz-btn-zoom]]:m-0 [&_[data-rmiz-btn-zoom]]:size-10 [&_[data-rmiz-btn-zoom]]:touch-manipulation [&_[data-rmiz-btn-zoom]]:appearance-none [&_[data-rmiz-btn-zoom]]:rounded-[50%] [&_[data-rmiz-btn-zoom]]:border-none [&_[data-rmiz-btn-zoom]]:bg-foreground/70 [&_[data-rmiz-btn-zoom]]:p-2 [&_[data-rmiz-btn-zoom]]:text-background [&_[data-rmiz-btn-zoom]]:outline-offset-2',\n      '[&_[data-rmiz-btn-unzoom]]:m-0 [&_[data-rmiz-btn-unzoom]]:size-10 [&_[data-rmiz-btn-unzoom]]:touch-manipulation [&_[data-rmiz-btn-unzoom]]:appearance-none [&_[data-rmiz-btn-unzoom]]:rounded-[50%] [&_[data-rmiz-btn-unzoom]]:border-none [&_[data-rmiz-btn-unzoom]]:bg-foreground/70 [&_[data-rmiz-btn-unzoom]]:p-2 [&_[data-rmiz-btn-unzoom]]:text-background [&_[data-rmiz-btn-unzoom]]:outline-offset-2',\n      '[&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:pointer-events-none [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:absolute [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:size-px [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:overflow-hidden [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:whitespace-nowrap [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:[clip-path:inset(50%)] [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:[clip:rect(0_0_0_0)]',\n      '[&_[data-rmiz-btn-zoom]]:absolute [&_[data-rmiz-btn-zoom]]:top-2.5 [&_[data-rmiz-btn-zoom]]:right-2.5 [&_[data-rmiz-btn-zoom]]:bottom-auto [&_[data-rmiz-btn-zoom]]:left-auto [&_[data-rmiz-btn-zoom]]:cursor-zoom-in',\n      '[&_[data-rmiz-btn-unzoom]]:absolute [&_[data-rmiz-btn-unzoom]]:top-5 [&_[data-rmiz-btn-unzoom]]:right-5 [&_[data-rmiz-btn-unzoom]]:bottom-auto [&_[data-rmiz-btn-unzoom]]:left-auto [&_[data-rmiz-btn-unzoom]]:z-[1] [&_[data-rmiz-btn-unzoom]]:cursor-zoom-out',\n      '[&_[data-rmiz-content=\"found\"]_img]:cursor-zoom-in',\n      '[&_[data-rmiz-content=\"found\"]_svg]:cursor-zoom-in',\n      '[&_[data-rmiz-content=\"found\"]_[role=\"img\"]]:cursor-zoom-in',\n      '[&_[data-rmiz-content=\"found\"]_[data-zoom]]:cursor-zoom-in',\n      className\n    )}\n  >\n    <Zoom\n      classDialog={cn(\n        '[&::backdrop]:hidden',\n        '[&[open]]:fixed [&[open]]:m-0 [&[open]]:h-dvh [&[open]]:max-h-none [&[open]]:w-dvw [&[open]]:max-w-none [&[open]]:overflow-hidden [&[open]]:border-0 [&[open]]:bg-transparent [&[open]]:p-0',\n        '[&_[data-rmiz-modal-overlay]]:absolute [&_[data-rmiz-modal-overlay]]:inset-0 [&_[data-rmiz-modal-overlay]]:transition-all',\n        '[&_[data-rmiz-modal-overlay=\"hidden\"]]:bg-transparent',\n        '[&_[data-rmiz-modal-overlay=\"visible\"]]:bg-background/80 [&_[data-rmiz-modal-overlay=\"visible\"]]:backdrop-blur-md',\n        '[&_[data-rmiz-modal-content]]:relative [&_[data-rmiz-modal-content]]:size-full',\n        '[&_[data-rmiz-modal-img]]:absolute [&_[data-rmiz-modal-img]]:origin-top-left [&_[data-rmiz-modal-img]]:cursor-zoom-out [&_[data-rmiz-modal-img]]:transition-transform',\n        'motion-reduce:[&_[data-rmiz-modal-img]]:transition-none motion-reduce:[&_[data-rmiz-modal-overlay]]:transition-none',\n        backdropClassName\n      )}\n      {...props}\n    />\n  </div>\n);\n"], "names": [], "mappings": ";;;;;AAEA;AAIA;AANA;;;;AAeO,MAAM,YAAY,CAAC,EACxB,SAAS,EACT,iBAAiB,EACjB,GAAG,OACY,iBACf,6WAAC;QACC,WAAW,IAAA,kHAAE,EACX,YACA,4EACA,4XACA,gZACA,odACA,yNACA,mQACA,sDACA,sDACA,+DACA,8DACA;kBAGF,cAAA,6WAAC,qRAAI;YACH,aAAa,IAAA,kHAAE,EACb,wBACA,+LACA,6HACA,yDACA,qHACA,kFACA,yKACA,uHACA;YAED,GAAG,KAAK", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/accordion.tsx"], "sourcesContent": ["'use client';\n\nimport * as AccordionPrimitive from '@radix-ui/react-accordion';\nimport { ChevronDownIcon } from 'lucide-react';\nimport type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Accordion({\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Root>) {\n  return <AccordionPrimitive.Root data-slot=\"accordion\" {...props} />;\n}\n\nfunction AccordionItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Item>) {\n  return (\n    <AccordionPrimitive.Item\n      className={cn('border-b last:border-b-0', className)}\n      data-slot=\"accordion-item\"\n      {...props}\n    />\n  );\n}\n\nfunction AccordionTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {\n  return (\n    <AccordionPrimitive.Header className=\"flex\">\n      <AccordionPrimitive.Trigger\n        className={cn(\n          'flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left font-medium text-sm outline-none transition-all hover:underline focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180',\n          className\n        )}\n        data-slot=\"accordion-trigger\"\n        {...props}\n      >\n        {children}\n        <ChevronDownIcon className=\"pointer-events-none size-4 shrink-0 translate-y-0.5 text-muted-foreground transition-transform duration-200\" />\n      </AccordionPrimitive.Trigger>\n    </AccordionPrimitive.Header>\n  );\n}\n\nfunction AccordionContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Content>) {\n  return (\n    <AccordionPrimitive.Content\n      className=\"overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\n      data-slot=\"accordion-content\"\n      {...props}\n    >\n      <div className={cn('pt-0 pb-4', className)}>{children}</div>\n    </AccordionPrimitive.Content>\n  );\n}\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent };\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AAGA;AANA;;;;;AAQA,SAAS,UAAU,EACjB,GAAG,OACkD;IACrD,qBAAO,6WAAC,oRAAuB;QAAC,aAAU;QAAa,GAAG,KAAK;;;;;;AACjE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6WAAC,oRAAuB;QACtB,WAAW,IAAA,kHAAE,EAAC,4BAA4B;QAC1C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,6WAAC,sRAAyB;QAAC,WAAU;kBACnC,cAAA,6WAAC,uRAA0B;YACzB,WAAW,IAAA,kHAAE,EACX,8SACA;YAEF,aAAU;YACT,GAAG,KAAK;;gBAER;8BACD,6WAAC,+TAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,6WAAC,uRAA0B;QACzB,WAAU;QACV,aAAU;QACT,GAAG,KAAK;kBAET,cAAA,6WAAC;YAAI,WAAW,IAAA,kHAAE,EAAC,aAAa;sBAAa;;;;;;;;;;;AAGnD", "debugId": null}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/infinite-slider.tsx"], "sourcesContent": ["'use client';\nimport { animate, motion, useMotionValue } from 'motion/react';\nimport { useEffect, useState } from 'react';\nimport useMeasure from 'react-use-measure';\nimport { cn } from '@/lib/utils';\n\nexport type InfiniteSliderProps = {\n  children: React.ReactNode;\n  gap?: number;\n  speed?: number;\n  speedOnHover?: number;\n  direction?: 'horizontal' | 'vertical';\n  reverse?: boolean;\n  className?: string;\n};\n\nexport function InfiniteSlider({\n  children,\n  gap = 16,\n  speed = 100,\n  speedOnHover,\n  direction = 'horizontal',\n  reverse = false,\n  className,\n}: InfiniteSliderProps) {\n  const [currentSpeed, setCurrentSpeed] = useState(speed);\n  const [ref, { width, height }] = useMeasure();\n  const translation = useMotionValue(0);\n  const [isTransitioning, setIsTransitioning] = useState(false);\n  const [key, setKey] = useState(0);\n\n  useEffect(() => {\n    let controls;\n    const size = direction === 'horizontal' ? width : height;\n    const contentSize = size + gap;\n    const from = reverse ? -contentSize / 2 : 0;\n    const to = reverse ? 0 : -contentSize / 2;\n\n    const distanceToTravel = Math.abs(to - from);\n    const duration = distanceToTravel / currentSpeed;\n\n    if (isTransitioning) {\n      const remainingDistance = Math.abs(translation.get() - to);\n      const transitionDuration = remainingDistance / currentSpeed;\n\n      controls = animate(translation, [translation.get(), to], {\n        ease: 'linear',\n        duration: transitionDuration,\n        onComplete: () => {\n          setIsTransitioning(false);\n          setKey((prevKey) => prevKey + 1);\n        },\n      });\n    } else {\n      controls = animate(translation, [from, to], {\n        ease: 'linear',\n        duration,\n        repeat: Number.POSITIVE_INFINITY,\n        repeatType: 'loop',\n        repeatDelay: 0,\n        onRepeat: () => {\n          translation.set(from);\n        },\n      });\n    }\n\n    return controls?.stop;\n  }, [\n    key,\n    translation,\n    currentSpeed,\n    width,\n    height,\n    gap,\n    isTransitioning,\n    direction,\n    reverse,\n  ]);\n\n  const hoverProps = speedOnHover\n    ? {\n        onHoverStart: () => {\n          setIsTransitioning(true);\n          setCurrentSpeed(speedOnHover);\n        },\n        onHoverEnd: () => {\n          setIsTransitioning(true);\n          setCurrentSpeed(speed);\n        },\n      }\n    : {};\n\n  return (\n    <div className={cn('overflow-hidden', className)}>\n      <motion.div\n        className=\"flex w-max\"\n        ref={ref}\n        style={{\n          ...(direction === 'horizontal'\n            ? { x: translation }\n            : { y: translation }),\n          gap: `${gap}px`,\n          flexDirection: direction === 'horizontal' ? 'row' : 'column',\n        }}\n        {...hoverProps}\n      >\n        {children}\n        {children}\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AAAA;AACA;AACA;AACA;AAJA;;;;;;AAgBO,SAAS,eAAe,EAC7B,QAAQ,EACR,MAAM,EAAE,EACR,QAAQ,GAAG,EACX,YAAY,EACZ,YAAY,YAAY,EACxB,UAAU,KAAK,EACf,SAAS,EACW;IACpB,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,gVAAQ,EAAC;IACjD,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,GAAG,IAAA,4QAAU;IAC3C,MAAM,cAAc,IAAA,0SAAc,EAAC;IACnC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,gVAAQ,EAAC;IACvD,MAAM,CAAC,KAAK,OAAO,GAAG,IAAA,gVAAQ,EAAC;IAE/B,IAAA,iVAAS,EAAC;QACR,IAAI;QACJ,MAAM,OAAO,cAAc,eAAe,QAAQ;QAClD,MAAM,cAAc,OAAO;QAC3B,MAAM,OAAO,UAAU,CAAC,cAAc,IAAI;QAC1C,MAAM,KAAK,UAAU,IAAI,CAAC,cAAc;QAExC,MAAM,mBAAmB,KAAK,GAAG,CAAC,KAAK;QACvC,MAAM,WAAW,mBAAmB;QAEpC,IAAI,iBAAiB;YACnB,MAAM,oBAAoB,KAAK,GAAG,CAAC,YAAY,GAAG,KAAK;YACvD,MAAM,qBAAqB,oBAAoB;YAE/C,WAAW,IAAA,iSAAO,EAAC,aAAa;gBAAC,YAAY,GAAG;gBAAI;aAAG,EAAE;gBACvD,MAAM;gBACN,UAAU;gBACV,YAAY;oBACV,mBAAmB;oBACnB,OAAO,CAAC,UAAY,UAAU;gBAChC;YACF;QACF,OAAO;YACL,WAAW,IAAA,iSAAO,EAAC,aAAa;gBAAC;gBAAM;aAAG,EAAE;gBAC1C,MAAM;gBACN;gBACA,QAAQ,OAAO,iBAAiB;gBAChC,YAAY;gBACZ,aAAa;gBACb,UAAU;oBACR,YAAY,GAAG,CAAC;gBAClB;YACF;QACF;QAEA,OAAO,UAAU;IACnB,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,aAAa,eACf;QACE,cAAc;YACZ,mBAAmB;YACnB,gBAAgB;QAClB;QACA,YAAY;YACV,mBAAmB;YACnB,gBAAgB;QAClB;IACF,IACA,CAAC;IAEL,qBACE,6WAAC;QAAI,WAAW,IAAA,kHAAE,EAAC,mBAAmB;kBACpC,cAAA,6WAAC,0SAAM,CAAC,GAAG;YACT,WAAU;YACV,KAAK;YACL,OAAO;gBACL,GAAI,cAAc,eACd;oBAAE,GAAG;gBAAY,IACjB;oBAAE,GAAG;gBAAY,CAAC;gBACtB,KAAK,GAAG,IAAI,EAAE,CAAC;gBACf,eAAe,cAAc,eAAe,QAAQ;YACtD;YACC,GAAG,UAAU;;gBAEb;gBACA;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/progressive-blur.tsx"], "sourcesContent": ["'use client';\nimport { type HTMLMotionProps, motion } from 'motion/react';\nimport { cn } from '@/lib/utils';\n\nexport const GRADIENT_ANGLES = {\n  top: 0,\n  right: 90,\n  bottom: 180,\n  left: 270,\n};\n\nexport type ProgressiveBlurProps = {\n  direction?: keyof typeof GRADIENT_ANGLES;\n  blurLayers?: number;\n  className?: string;\n  blurIntensity?: number;\n} & HTMLMotionProps<'div'>;\n\nexport function ProgressiveBlur({\n  direction = 'bottom',\n  blurLayers = 8,\n  className,\n  blurIntensity = 0.25,\n  ...props\n}: ProgressiveBlurProps) {\n  const layers = Math.max(blurLayers, 2);\n  const segmentSize = 1 / (blurLayers + 1);\n\n  return (\n    <div className={cn('relative', className)}>\n      {Array.from({ length: layers }).map((_, index) => {\n        const angle = GRADIENT_ANGLES[direction];\n        const gradientStops = [\n          index * segmentSize,\n          (index + 1) * segmentSize,\n          (index + 2) * segmentSize,\n          (index + 3) * segmentSize,\n        ].map(\n          (pos, posIndex) =>\n            `rgba(255, 255, 255, ${posIndex === 1 || posIndex === 2 ? 1 : 0}) ${pos * 100}%`\n        );\n\n        const gradient = `linear-gradient(${angle}deg, ${gradientStops.join(\n          ', '\n        )})`;\n\n        return (\n          <motion.div\n            className=\"pointer-events-none absolute inset-0 rounded-[inherit]\"\n            key={index}\n            style={{\n              maskImage: gradient,\n              WebkitMaskImage: gradient,\n              backdropFilter: `blur(${index * blurIntensity}px)`,\n              WebkitBackdropFilter: `blur(${index * blurIntensity}px)`,\n            }}\n            {...props}\n          />\n        );\n      })}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAFA;;;;AAIO,MAAM,kBAAkB;IAC7B,KAAK;IACL,OAAO;IACP,QAAQ;IACR,MAAM;AACR;AASO,SAAS,gBAAgB,EAC9B,YAAY,QAAQ,EACpB,aAAa,CAAC,EACd,SAAS,EACT,gBAAgB,IAAI,EACpB,GAAG,OACkB;IACrB,MAAM,SAAS,KAAK,GAAG,CAAC,YAAY;IACpC,MAAM,cAAc,IAAI,CAAC,aAAa,CAAC;IAEvC,qBACE,6WAAC;QAAI,WAAW,IAAA,kHAAE,EAAC,YAAY;kBAC5B,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAO,GAAG,GAAG,CAAC,CAAC,GAAG;YACtC,MAAM,QAAQ,eAAe,CAAC,UAAU;YACxC,MAAM,gBAAgB;gBACpB,QAAQ;gBACR,CAAC,QAAQ,CAAC,IAAI;gBACd,CAAC,QAAQ,CAAC,IAAI;gBACd,CAAC,QAAQ,CAAC,IAAI;aACf,CAAC,GAAG,CACH,CAAC,KAAK,WACJ,CAAC,oBAAoB,EAAE,aAAa,KAAK,aAAa,IAAI,IAAI,EAAE,EAAE,EAAE,MAAM,IAAI,CAAC,CAAC;YAGpF,MAAM,WAAW,CAAC,gBAAgB,EAAE,MAAM,KAAK,EAAE,cAAc,IAAI,CACjE,MACA,CAAC,CAAC;YAEJ,qBACE,6WAAC,0SAAM,CAAC,GAAG;gBACT,WAAU;gBAEV,OAAO;oBACL,WAAW;oBACX,iBAAiB;oBACjB,gBAAgB,CAAC,KAAK,EAAE,QAAQ,cAAc,GAAG,CAAC;oBAClD,sBAAsB,CAAC,KAAK,EAAE,QAAQ,cAAc,GAAG,CAAC;gBAC1D;gBACC,GAAG,KAAK;eAPJ;;;;;QAUX;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 618, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/carousel.tsx"], "sourcesContent": ["'use client';\n\nimport useEmblaCarousel, {\n  type UseEmblaCarouselType,\n} from 'embla-carousel-react';\nimport { ArrowLeft, ArrowRight } from 'lucide-react';\nimport React from 'react';\nimport { Button } from '@/components/ui/button';\nimport { cn } from '@/lib/utils';\n\ntype CarouselApi = UseEmblaCarouselType[1];\ntype UseCarouselParameters = Parameters<typeof useEmblaCarousel>;\ntype CarouselOptions = UseCarouselParameters[0];\ntype CarouselPlugin = UseCarouselParameters[1];\n\ntype CarouselProps = {\n  opts?: CarouselOptions;\n  plugins?: CarouselPlugin;\n  orientation?: 'horizontal' | 'vertical';\n  setApi?: (api: CarouselApi) => void;\n};\n\ntype CarouselContextProps = {\n  carouselRef: ReturnType<typeof useEmblaCarousel>[0];\n  api: ReturnType<typeof useEmblaCarousel>[1];\n  scrollPrev: () => void;\n  scrollNext: () => void;\n  canScrollPrev: boolean;\n  canScrollNext: boolean;\n} & CarouselProps;\n\nconst CarouselContext = React.createContext<CarouselContextProps | null>(null);\n\nexport function useCarousel() {\n  const context = React.useContext(CarouselContext);\n\n  if (!context) {\n    throw new Error('useCarousel must be used within a <Carousel />');\n  }\n\n  return context;\n}\n\nfunction Carousel({\n  orientation = 'horizontal',\n  opts,\n  setApi,\n  plugins,\n  className,\n  children,\n  ...props\n}: React.ComponentProps<'div'> & CarouselProps) {\n  const [carouselRef, api] = useEmblaCarousel(\n    {\n      ...opts,\n      axis: orientation === 'horizontal' ? 'x' : 'y',\n    },\n    plugins\n  );\n  const [canScrollPrev, setCanScrollPrev] = React.useState(false);\n  const [canScrollNext, setCanScrollNext] = React.useState(false);\n\n  const onSelect = React.useCallback((api: CarouselApi) => {\n    if (!api) return;\n    setCanScrollPrev(api.canScrollPrev());\n    setCanScrollNext(api.canScrollNext());\n  }, []);\n\n  const scrollPrev = React.useCallback(() => {\n    api?.scrollPrev();\n  }, [api]);\n\n  const scrollNext = React.useCallback(() => {\n    api?.scrollNext();\n  }, [api]);\n\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent<HTMLDivElement>) => {\n      if (event.key === 'ArrowLeft') {\n        event.preventDefault();\n        scrollPrev();\n      } else if (event.key === 'ArrowRight') {\n        event.preventDefault();\n        scrollNext();\n      }\n    },\n    [scrollPrev, scrollNext]\n  );\n\n  React.useEffect(() => {\n    if (!(api && setApi)) return;\n    setApi(api);\n  }, [api, setApi]);\n\n  React.useEffect(() => {\n    if (!api) return;\n    onSelect(api);\n    api.on('reInit', onSelect);\n    api.on('select', onSelect);\n\n    return () => {\n      api?.off('select', onSelect);\n    };\n  }, [api, onSelect]);\n\n  return (\n    <CarouselContext.Provider\n      value={{\n        carouselRef,\n        api,\n        opts,\n        orientation:\n          orientation || (opts?.axis === 'y' ? 'vertical' : 'horizontal'),\n        scrollPrev,\n        scrollNext,\n        canScrollPrev,\n        canScrollNext,\n      }}\n    >\n      <div\n        aria-roledescription=\"carousel\"\n        className={cn('relative', className)}\n        data-slot=\"carousel\"\n        onKeyDownCapture={handleKeyDown}\n        role=\"region\"\n        {...props}\n      >\n        {children}\n      </div>\n    </CarouselContext.Provider>\n  );\n}\n\nfunction CarouselContent({ className, ...props }: React.ComponentProps<'div'>) {\n  const { carouselRef, orientation } = useCarousel();\n\n  return (\n    <div\n      className=\"overflow-hidden\"\n      data-slot=\"carousel-content\"\n      ref={carouselRef}\n    >\n      <div\n        className={cn(\n          'flex',\n          orientation === 'horizontal' ? '-ml-4' : '-mt-4 flex-col',\n          className\n        )}\n        {...props}\n      />\n    </div>\n  );\n}\n\nfunction CarouselItem({ className, ...props }: React.ComponentProps<'div'>) {\n  const { orientation } = useCarousel();\n\n  return (\n    <div\n      aria-roledescription=\"slide\"\n      className={cn(\n        'min-w-0 shrink-0 grow-0 basis-full',\n        orientation === 'horizontal' ? 'pl-4' : 'pt-4',\n        className\n      )}\n      data-slot=\"carousel-item\"\n      role=\"group\"\n      {...props}\n    />\n  );\n}\n\nfunction CarouselPrevious({\n  className,\n  variant = 'outline',\n  size = 'icon',\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const { orientation, scrollPrev, canScrollPrev } = useCarousel();\n\n  return (\n    <Button\n      className={cn(\n        'absolute size-8 rounded-full',\n        orientation === 'horizontal'\n          ? '-left-12 -translate-y-1/2 top-1/2'\n          : '-top-12 -translate-x-1/2 left-1/2 rotate-90',\n        className\n      )}\n      data-slot=\"carousel-previous\"\n      disabled={!canScrollPrev}\n      onClick={scrollPrev}\n      size={size}\n      variant={variant}\n      {...props}\n    >\n      <ArrowLeft />\n      <span className=\"sr-only\">Previous slide</span>\n    </Button>\n  );\n}\n\nfunction CarouselNext({\n  className,\n  variant = 'outline',\n  size = 'icon',\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const { orientation, scrollNext, canScrollNext } = useCarousel();\n\n  return (\n    <Button\n      className={cn(\n        'absolute size-8 rounded-full',\n        orientation === 'horizontal'\n          ? '-right-12 -translate-y-1/2 top-1/2'\n          : '-bottom-12 -translate-x-1/2 left-1/2 rotate-90',\n        className\n      )}\n      data-slot=\"carousel-next\"\n      disabled={!canScrollNext}\n      onClick={scrollNext}\n      size={size}\n      variant={variant}\n      {...props}\n    >\n      <ArrowRight />\n      <span className=\"sr-only\">Next slide</span>\n    </Button>\n  );\n}\n\nexport {\n  type CarouselApi,\n  Carousel,\n  CarouselContent,\n  CarouselItem,\n  CarouselPrevious,\n  CarouselNext,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAEA;AAGA;AAAA;AACA;AACA;AACA;AARA;;;;;;;AA+BA,MAAM,gCAAkB,+UAAK,CAAC,aAAa,CAA8B;AAElE,SAAS;IACd,MAAM,UAAU,+UAAK,CAAC,UAAU,CAAC;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,SAAS,EAChB,cAAc,YAAY,EAC1B,IAAI,EACJ,MAAM,EACN,OAAO,EACP,SAAS,EACT,QAAQ,EACR,GAAG,OACyC;IAC5C,MAAM,CAAC,aAAa,IAAI,GAAG,IAAA,8RAAgB,EACzC;QACE,GAAG,IAAI;QACP,MAAM,gBAAgB,eAAe,MAAM;IAC7C,GACA;IAEF,MAAM,CAAC,eAAe,iBAAiB,GAAG,+UAAK,CAAC,QAAQ,CAAC;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,+UAAK,CAAC,QAAQ,CAAC;IAEzD,MAAM,WAAW,+UAAK,CAAC,WAAW,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK;QACV,iBAAiB,IAAI,aAAa;QAClC,iBAAiB,IAAI,aAAa;IACpC,GAAG,EAAE;IAEL,MAAM,aAAa,+UAAK,CAAC,WAAW,CAAC;QACnC,KAAK;IACP,GAAG;QAAC;KAAI;IAER,MAAM,aAAa,+UAAK,CAAC,WAAW,CAAC;QACnC,KAAK;IACP,GAAG;QAAC;KAAI;IAER,MAAM,gBAAgB,+UAAK,CAAC,WAAW,CACrC,CAAC;QACC,IAAI,MAAM,GAAG,KAAK,aAAa;YAC7B,MAAM,cAAc;YACpB;QACF,OAAO,IAAI,MAAM,GAAG,KAAK,cAAc;YACrC,MAAM,cAAc;YACpB;QACF;IACF,GACA;QAAC;QAAY;KAAW;IAG1B,+UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,CAAC,OAAO,MAAM,GAAG;QACtB,OAAO;IACT,GAAG;QAAC;QAAK;KAAO;IAEhB,+UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,KAAK;QACV,SAAS;QACT,IAAI,EAAE,CAAC,UAAU;QACjB,IAAI,EAAE,CAAC,UAAU;QAEjB,OAAO;YACL,KAAK,IAAI,UAAU;QACrB;IACF,GAAG;QAAC;QAAK;KAAS;IAElB,qBACE,6WAAC,gBAAgB,QAAQ;QACvB,OAAO;YACL;YACA;YACA;YACA,aACE,eAAe,CAAC,MAAM,SAAS,MAAM,aAAa,YAAY;YAChE;YACA;YACA;YACA;QACF;kBAEA,cAAA,6WAAC;YACC,wBAAqB;YACrB,WAAW,IAAA,kHAAE,EAAC,YAAY;YAC1B,aAAU;YACV,kBAAkB;YAClB,MAAK;YACJ,GAAG,KAAK;sBAER;;;;;;;;;;;AAIT;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;IAErC,qBACE,6WAAC;QACC,WAAU;QACV,aAAU;QACV,KAAK;kBAEL,cAAA,6WAAC;YACC,WAAW,IAAA,kHAAE,EACX,QACA,gBAAgB,eAAe,UAAU,kBACzC;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,MAAM,EAAE,WAAW,EAAE,GAAG;IAExB,qBACE,6WAAC;QACC,wBAAqB;QACrB,WAAW,IAAA,kHAAE,EACX,sCACA,gBAAgB,eAAe,SAAS,QACxC;QAEF,aAAU;QACV,MAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,MAAM,EACb,GAAG,OACiC;IACpC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACE,6WAAC,qIAAM;QACL,WAAW,IAAA,kHAAE,EACX,gCACA,gBAAgB,eACZ,sCACA,+CACJ;QAEF,aAAU;QACV,UAAU,CAAC;QACX,SAAS;QACT,MAAM;QACN,SAAS;QACR,GAAG,KAAK;;0BAET,6WAAC,iTAAS;;;;;0BACV,6WAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,MAAM,EACb,GAAG,OACiC;IACpC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACE,6WAAC,qIAAM;QACL,WAAW,IAAA,kHAAE,EACX,gCACA,gBAAgB,eACZ,uCACA,kDACJ;QAEF,aAAU;QACV,UAAU,CAAC;QACX,SAAS;QACT,MAAM;QACN,SAAS;QACR,GAAG,KAAK;;0BAET,6WAAC,oTAAU;;;;;0BACX,6WAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 838, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["import Image from 'next/image';\nimport { cn } from '@/lib/utils';\n\nexport const ReviewCard = ({\n  img,\n  name,\n  username,\n  body,\n}: {\n  img: string;\n  name: string;\n  username: string;\n  body: string;\n}) => {\n  return (\n    <figure\n      className={cn(\n        \"group flex max-h-72 w-fit flex-shrink-0 flex-col justify-between gap-8 rounded-xl bg-muted/40 p-4 px-5 opacity-60 shadow-sm transition-all duration-300 hover:bg-[url('/gradient.webp')] hover:bg-center hover:bg-cover hover:bg-no-repeat hover:opacity-100 hover:shadow-md md:w-[28rem] md:p-6 md:px-7 [&:hover_.avatar-ring]:ring-brandYellow [&:hover_.external-link-icon]:opacity-100 [&:hover_.social-icon-border]:border-brand-600\"\n      )}\n    >\n      <blockquote className=\"mt-2 font-medium text-md text-primary tracking-normal\">\n        {body}\n        One of the most delightful, inventive, powerful new interfaces I've\n        tried in years. Actually feels like an AI native computer.\n      </blockquote>\n      <div className=\"relative flex w-full items-center gap-3 text-left transition-all duration-300 hover:scale-[98%]\">\n        <Image\n          alt=\"\"\n          className=\"size-12 rounded-full\"\n          height=\"48\"\n          src={img}\n          width=\"48\"\n        />\n\n        <div className=\"flex-1\">\n          <figcaption className=\"font-aeonik font-medium text-lg text-primary/80 tracking-normal\">\n            {name}\n          </figcaption>\n          <p className=\"font-aeonik font-normal text-md text-primary/50 tracking-tight\">\n            {username}\n          </p>\n        </div>\n      </div>\n    </figure>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEO,MAAM,aAAa,CAAC,EACzB,GAAG,EACH,IAAI,EACJ,QAAQ,EACR,IAAI,EAML;IACC,qBACE,6WAAC;QACC,WAAW,IAAA,kHAAE,EACX;;0BAGF,6WAAC;gBAAW,WAAU;;oBACnB;oBAAK;;;;;;;0BAIR,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,uQAAK;wBACJ,KAAI;wBACJ,WAAU;wBACV,QAAO;wBACP,KAAK;wBACL,OAAM;;;;;;kCAGR,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAW,WAAU;0CACnB;;;;;;0CAEH,6WAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 919, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/home/<USER>"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport {\n  Carousel,\n  CarouselContent,\n  CarouselItem,\n  CarouselNext,\n  CarouselPrevious,\n  useCarousel,\n} from '@/components/ui/carousel';\nimport { reviews } from '@/config/docs';\nimport { ReviewCard } from './review-card';\n\nfunction CarouselDots() {\n  const { api } = useCarousel();\n  const [selectedIndex, setSelectedIndex] = useState(0);\n  const [count, setCount] = useState(0);\n\n  useEffect(() => {\n    if (!api) return;\n\n    setCount(api.scrollSnapList().length);\n\n    const onSelect = () => {\n      setSelectedIndex(api.selectedScrollSnap());\n    };\n\n    api.on('select', onSelect);\n    onSelect();\n\n    return () => {\n      api.off('select', onSelect);\n    };\n  }, [api]);\n\n  return (\n    <div className=\"mt-4 flex justify-center gap-2\">\n      {Array.from({ length: count }).map((_, i) => (\n        <button\n          className={`h-2 rounded-full transition-all ${\n            i === selectedIndex ? 'w-8 bg-brand-600' : 'w-2 bg-muted'\n          }`}\n          key={i}\n          onClick={() => api?.scrollTo(i)}\n          type=\"button\"\n        />\n      ))}\n    </div>\n  );\n}\n\nexport default function RevieCardsMobile() {\n  return (\n    <div className=\"md:hidden\">\n      <Carousel>\n        <CarouselContent>\n          {reviews.map((review) => (\n            <CarouselItem key={review.name}>\n              <ReviewCard {...review} />\n            </CarouselItem>\n          ))}\n        </CarouselContent>\n        <CarouselPrevious />\n        <CarouselNext />\n        <CarouselDots />\n      </Carousel>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAQA;AACA;AAZA;;;;;;AAcA,SAAS;IACP,MAAM,EAAE,GAAG,EAAE,GAAG,IAAA,4IAAW;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,gVAAQ,EAAC;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,gVAAQ,EAAC;IAEnC,IAAA,iVAAS,EAAC;QACR,IAAI,CAAC,KAAK;QAEV,SAAS,IAAI,cAAc,GAAG,MAAM;QAEpC,MAAM,WAAW;YACf,iBAAiB,IAAI,kBAAkB;QACzC;QAEA,IAAI,EAAE,CAAC,UAAU;QACjB;QAEA,OAAO;YACL,IAAI,GAAG,CAAC,UAAU;QACpB;IACF,GAAG;QAAC;KAAI;IAER,qBACE,6WAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,kBACrC,6WAAC;gBACC,WAAW,CAAC,gCAAgC,EAC1C,MAAM,gBAAgB,qBAAqB,gBAC3C;gBAEF,SAAS,IAAM,KAAK,SAAS;gBAC7B,MAAK;eAFA;;;;;;;;;;AAOf;AAEe,SAAS;IACtB,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC,yIAAQ;;8BACP,6WAAC,gJAAe;8BACb,yHAAO,CAAC,GAAG,CAAC,CAAC,uBACZ,6WAAC,6IAAY;sCACX,cAAA,6WAAC,iJAAU;gCAAE,GAAG,MAAM;;;;;;2BADL,OAAO,IAAI;;;;;;;;;;8BAKlC,6WAAC,iJAAgB;;;;;8BACjB,6WAAC,6IAAY;;;;;8BACb,6WAAC;;;;;;;;;;;;;;;;AAIT", "debugId": null}}]}